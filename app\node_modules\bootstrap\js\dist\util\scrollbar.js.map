{"version": 3, "file": "scrollbar.js", "sources": ["../../src/util/scrollbar.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Manipulator from '../dom/manipulator.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport { isElement } from './index.js'\n\n/**\n * Constants\n */\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\nconst PROPERTY_PADDING = 'padding-right'\nconst PROPERTY_MARGIN = 'margin-right'\n\n/**\n * Class definition\n */\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  // Public\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN, calculatedValue => calculatedValue - width)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN)\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n\n  // Private\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProperty, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProperty)\n      const calculatedValue = window.getComputedStyle(element).getPropertyValue(styleProperty)\n      element.style.setProperty(styleProperty, `${callback(Number.parseFloat(calculatedValue))}px`)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _saveInitialAttribute(element, styleProperty) {\n    const actualValue = element.style.getPropertyValue(styleProperty)\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProperty, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProperty) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProperty)\n      // We only want to remove the property if the value is `null`; the value can also be zero\n      if (value === null) {\n        element.style.removeProperty(styleProperty)\n        return\n      }\n\n      Manipulator.removeDataAttribute(element, styleProperty)\n      element.style.setProperty(styleProperty, value)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n      return\n    }\n\n    for (const sel of SelectorEngine.find(selector, this._element)) {\n      callBack(sel)\n    }\n  }\n}\n\nexport default ScrollBarHelper\n"], "names": ["SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "PROPERTY_PADDING", "PROPERTY_MARGIN", "ScrollBarHelper", "constructor", "_element", "document", "body", "getWidth", "documentWidth", "documentElement", "clientWidth", "Math", "abs", "window", "innerWidth", "hide", "width", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "reset", "_resetElementAttributes", "isOverflowing", "_saveInitialAttribute", "style", "overflow", "selector", "styleProperty", "callback", "scrollbarWidth", "manipulationCallBack", "element", "getComputedStyle", "getPropertyValue", "setProperty", "Number", "parseFloat", "_applyManipulationCallback", "actualValue", "Manipulator", "setDataAttribute", "value", "getDataAttribute", "removeProperty", "removeDataAttribute", "callBack", "isElement", "sel", "SelectorEngine", "find"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;;EAMA;EACA;EACA;;EAEA,MAAMA,sBAAsB,GAAG,mDAAmD;EAClF,MAAMC,uBAAuB,GAAG,aAAa;EAC7C,MAAMC,gBAAgB,GAAG,eAAe;EACxC,MAAMC,eAAe,GAAG,cAAc;;EAEtC;EACA;EACA;;EAEA,MAAMC,eAAe,CAAC;EACpBC,EAAAA,WAAWA,GAAG;EACZ,IAAA,IAAI,CAACC,QAAQ,GAAGC,QAAQ,CAACC,IAAI;EAC/B,EAAA;;EAEA;EACAC,EAAAA,QAAQA,GAAG;EACT;EACA,IAAA,MAAMC,aAAa,GAAGH,QAAQ,CAACI,eAAe,CAACC,WAAW;MAC1D,OAAOC,IAAI,CAACC,GAAG,CAACC,MAAM,CAACC,UAAU,GAAGN,aAAa,CAAC;EACpD,EAAA;EAEAO,EAAAA,IAAIA,GAAG;EACL,IAAA,MAAMC,KAAK,GAAG,IAAI,CAACT,QAAQ,EAAE;MAC7B,IAAI,CAACU,gBAAgB,EAAE;EACvB;EACA,IAAA,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAACd,QAAQ,EAAEJ,gBAAgB,EAAEmB,eAAe,IAAIA,eAAe,GAAGH,KAAK,CAAC;EACvG;EACA,IAAA,IAAI,CAACE,qBAAqB,CAACpB,sBAAsB,EAAEE,gBAAgB,EAAEmB,eAAe,IAAIA,eAAe,GAAGH,KAAK,CAAC;EAChH,IAAA,IAAI,CAACE,qBAAqB,CAACnB,uBAAuB,EAAEE,eAAe,EAAEkB,eAAe,IAAIA,eAAe,GAAGH,KAAK,CAAC;EAClH,EAAA;EAEAI,EAAAA,KAAKA,GAAG;MACN,IAAI,CAACC,uBAAuB,CAAC,IAAI,CAACjB,QAAQ,EAAE,UAAU,CAAC;MACvD,IAAI,CAACiB,uBAAuB,CAAC,IAAI,CAACjB,QAAQ,EAAEJ,gBAAgB,CAAC;EAC7D,IAAA,IAAI,CAACqB,uBAAuB,CAACvB,sBAAsB,EAAEE,gBAAgB,CAAC;EACtE,IAAA,IAAI,CAACqB,uBAAuB,CAACtB,uBAAuB,EAAEE,eAAe,CAAC;EACxE,EAAA;EAEAqB,EAAAA,aAAaA,GAAG;EACd,IAAA,OAAO,IAAI,CAACf,QAAQ,EAAE,GAAG,CAAC;EAC5B,EAAA;;EAEA;EACAU,EAAAA,gBAAgBA,GAAG;MACjB,IAAI,CAACM,qBAAqB,CAAC,IAAI,CAACnB,QAAQ,EAAE,UAAU,CAAC;EACrD,IAAA,IAAI,CAACA,QAAQ,CAACoB,KAAK,CAACC,QAAQ,GAAG,QAAQ;EACzC,EAAA;EAEAP,EAAAA,qBAAqBA,CAACQ,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAE;EACvD,IAAA,MAAMC,cAAc,GAAG,IAAI,CAACtB,QAAQ,EAAE;MACtC,MAAMuB,oBAAoB,GAAGC,OAAO,IAAI;EACtC,MAAA,IAAIA,OAAO,KAAK,IAAI,CAAC3B,QAAQ,IAAIS,MAAM,CAACC,UAAU,GAAGiB,OAAO,CAACrB,WAAW,GAAGmB,cAAc,EAAE;EACzF,QAAA;EACF,MAAA;EAEA,MAAA,IAAI,CAACN,qBAAqB,CAACQ,OAAO,EAAEJ,aAAa,CAAC;EAClD,MAAA,MAAMR,eAAe,GAAGN,MAAM,CAACmB,gBAAgB,CAACD,OAAO,CAAC,CAACE,gBAAgB,CAACN,aAAa,CAAC;EACxFI,MAAAA,OAAO,CAACP,KAAK,CAACU,WAAW,CAACP,aAAa,EAAE,CAAA,EAAGC,QAAQ,CAACO,MAAM,CAACC,UAAU,CAACjB,eAAe,CAAC,CAAC,IAAI,CAAC;MAC/F,CAAC;EAED,IAAA,IAAI,CAACkB,0BAA0B,CAACX,QAAQ,EAAEI,oBAAoB,CAAC;EACjE,EAAA;EAEAP,EAAAA,qBAAqBA,CAACQ,OAAO,EAAEJ,aAAa,EAAE;MAC5C,MAAMW,WAAW,GAAGP,OAAO,CAACP,KAAK,CAACS,gBAAgB,CAACN,aAAa,CAAC;EACjE,IAAA,IAAIW,WAAW,EAAE;QACfC,WAAW,CAACC,gBAAgB,CAACT,OAAO,EAAEJ,aAAa,EAAEW,WAAW,CAAC;EACnE,IAAA;EACF,EAAA;EAEAjB,EAAAA,uBAAuBA,CAACK,QAAQ,EAAEC,aAAa,EAAE;MAC/C,MAAMG,oBAAoB,GAAGC,OAAO,IAAI;QACtC,MAAMU,KAAK,GAAGF,WAAW,CAACG,gBAAgB,CAACX,OAAO,EAAEJ,aAAa,CAAC;EAClE;QACA,IAAIc,KAAK,KAAK,IAAI,EAAE;EAClBV,QAAAA,OAAO,CAACP,KAAK,CAACmB,cAAc,CAAChB,aAAa,CAAC;EAC3C,QAAA;EACF,MAAA;EAEAY,MAAAA,WAAW,CAACK,mBAAmB,CAACb,OAAO,EAAEJ,aAAa,CAAC;QACvDI,OAAO,CAACP,KAAK,CAACU,WAAW,CAACP,aAAa,EAAEc,KAAK,CAAC;MACjD,CAAC;EAED,IAAA,IAAI,CAACJ,0BAA0B,CAACX,QAAQ,EAAEI,oBAAoB,CAAC;EACjE,EAAA;EAEAO,EAAAA,0BAA0BA,CAACX,QAAQ,EAAEmB,QAAQ,EAAE;EAC7C,IAAA,IAAIC,kBAAS,CAACpB,QAAQ,CAAC,EAAE;QACvBmB,QAAQ,CAACnB,QAAQ,CAAC;EAClB,MAAA;EACF,IAAA;EAEA,IAAA,KAAK,MAAMqB,GAAG,IAAIC,cAAc,CAACC,IAAI,CAACvB,QAAQ,EAAE,IAAI,CAACtB,QAAQ,CAAC,EAAE;QAC9DyC,QAAQ,CAACE,GAAG,CAAC;EACf,IAAA;EACF,EAAA;EACF;;;;;;;;"}