{"name": "react-bootstrap", "version": "2.10.10", "description": "Bootstrap 5 components built with React", "keywords": ["bootstrap", "react", "component", "components", "ecosystem-react", "react-component"], "homepage": "https://react-bootstrap.github.io/", "bugs": {"url": "https://github.com/react-bootstrap/react-bootstrap/issues"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "cjs/index.js", "module": "esm/index.js", "types": "esm/index.d.ts", "sideEffects": false, "repository": {"type": "git", "url": "git+https://github.com/react-bootstrap/react-bootstrap.git"}, "lint-staged": {"*.{js,ts,tsx}": "eslint --fix"}, "prettier": {"singleQuote": true}, "dependencies": {"@babel/runtime": "^7.24.7", "@restart/hooks": "^0.4.9", "@restart/ui": "^1.9.4", "@types/prop-types": "^15.7.12", "@types/react-transition-group": "^4.4.6", "classnames": "^2.3.2", "dom-helpers": "^5.2.1", "invariant": "^2.2.4", "prop-types": "^15.8.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.5", "uncontrollable": "^7.2.1", "warning": "^4.0.3"}, "peerDependencies": {"@types/react": ">=16.14.8", "react": ">=16.14.0", "react-dom": ">=16.14.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "publishConfig": {"directory": "lib"}, "release": {"conventionalCommits": true}, "packageManager": "yarn@1.22.19+sha1.4ba7fc5c6e704fce2066ecbfb0b0d8976fe62447"}