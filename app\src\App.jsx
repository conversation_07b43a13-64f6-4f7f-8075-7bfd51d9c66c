import { useState } from 'react'
import './App.css'

function App() {
  const [products, setProducts] = useState([
    { id: 1, name: 'IPAD', price: 550, quantity: 0, image: '📱' },
    { id: 2, name: 'LAPTOP', price: 250, quantity: 0, image: '💻' },
    { id: 3, name: 'TV', price: 200, quantity: 0, image: '📺' },
    { id: 4, name: 'PHONE', price: 400, quantity: 0, image: '📱' }
  ]);

  const increment = (id) => {
    setProducts(products.map(product =>
      product.id === id
        ? { ...product, quantity: product.quantity + 1 }
        : product
    ));
  };

  const decrement = (id) => {
    setProducts(products.map(product =>
      product.id === id && product.quantity > 0
        ? { ...product, quantity: product.quantity - 1 }
        : product
    ));
  };

  const deleteProduct = (id) => {
    setProducts(products.filter(product => product.id !== id));
  };

  const calculateTotal = () => {
    return products.reduce((total, product) => total + (product.price * product.quantity), 0);
  };

  return (
    <div className="app">
      <div className="products-container">
        {products.map(product => (
          <div key={product.id} className="product-card">
            <div className="product-image">
              {product.image}
            </div>
            <div className="product-info">
              <h3>{product.name}</h3>
              <p>{product.price} $</p>
            </div>
            <div className="quantity-controls">
              <button onClick={() => decrement(product.id)} className="quantity-btn">-</button>
              <span className="quantity">{product.quantity}</span>
              <button onClick={() => increment(product.id)} className="quantity-btn">+</button>
            </div>
            <button onClick={() => deleteProduct(product.id)} className="delete-btn">
              Delete
            </button>
          </div>
        ))}
      </div>
      <div className="total">
        Total: {calculateTotal()} $
      </div>
    </div>
  )
}

export default App
