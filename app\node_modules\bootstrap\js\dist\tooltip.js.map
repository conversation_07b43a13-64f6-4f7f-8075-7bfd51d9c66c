{"version": 3, "file": "tooltip.js", "sources": ["../src/tooltip.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport {\n  defineJQueryPlugin, execute, findShadowRoot, getElement, getUID, isRTL, noop\n} from './util/index.js'\nimport { DefaultAllowlist } from './util/sanitizer.js'\nimport TemplateFactory from './util/template-factory.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'tooltip'\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`\n\nconst EVENT_MODAL_HIDE = 'hide.bs.modal'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\nconst EVENT_HIDE = 'hide'\nconst EVENT_HIDDEN = 'hidden'\nconst EVENT_SHOW = 'show'\nconst EVENT_SHOWN = 'shown'\nconst EVENT_INSERTED = 'inserted'\nconst EVENT_CLICK = 'click'\nconst EVENT_FOCUSIN = 'focusin'\nconst EVENT_FOCUSOUT = 'focusout'\nconst EVENT_MOUSEENTER = 'mouseenter'\nconst EVENT_MOUSELEAVE = 'mouseleave'\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  animation: true,\n  boundary: 'clippingParents',\n  container: false,\n  customClass: '',\n  delay: 0,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  html: false,\n  offset: [0, 6],\n  placement: 'top',\n  popperConfig: null,\n  sanitize: true,\n  sanitizeFn: null,\n  selector: false,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n            '<div class=\"tooltip-arrow\"></div>' +\n            '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  title: '',\n  trigger: 'hover focus'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  animation: 'boolean',\n  boundary: '(string|element)',\n  container: '(string|element|boolean)',\n  customClass: '(string|function)',\n  delay: '(number|object)',\n  fallbackPlacements: 'array',\n  html: 'boolean',\n  offset: '(array|string|function)',\n  placement: '(string|function)',\n  popperConfig: '(null|object|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  selector: '(string|boolean)',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string'\n}\n\n/**\n * Class definition\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org/docs/v2/)')\n    }\n\n    super(element, config)\n\n    // Private\n    this._isEnabled = true\n    this._timeout = 0\n    this._isHovered = null\n    this._activeTrigger = {}\n    this._popper = null\n    this._templateFactory = null\n    this._newContent = null\n\n    // Protected\n    this.tip = null\n\n    this._setListeners()\n\n    if (!this._config.selector) {\n      this._fixTitle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle() {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (this._isShown()) {\n      this._leave()\n      return\n    }\n\n    this._enter()\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this._element.getAttribute('data-bs-original-title')) {\n      this._element.setAttribute('title', this._element.getAttribute('data-bs-original-title'))\n    }\n\n    this._disposePopper()\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this._isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOW))\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = (shadowRoot || this._element.ownerDocument.documentElement).contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    // TODO: v6 remove this or make it optional\n    this._disposePopper()\n\n    const tip = this._getTipElement()\n\n    this._element.setAttribute('aria-describedby', tip.getAttribute('id'))\n\n    const { container } = this._config\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip)\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_INSERTED))\n    }\n\n    this._popper = this._createPopper(tip)\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    const complete = () => {\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOWN))\n\n      if (this._isHovered === false) {\n        this._leave()\n      }\n\n      this._isHovered = false\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  hide() {\n    if (!this._isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDE))\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const tip = this._getTipElement()\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n    this._isHovered = null // it is a trick to support manual triggering\n\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (!this._isHovered) {\n        this._disposePopper()\n      }\n\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDDEN))\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  update() {\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n  _isWithContent() {\n    return Boolean(this._getTitle())\n  }\n\n  _getTipElement() {\n    if (!this.tip) {\n      this.tip = this._createTipElement(this._newContent || this._getContentForTemplate())\n    }\n\n    return this.tip\n  }\n\n  _createTipElement(content) {\n    const tip = this._getTemplateFactory(content).toHtml()\n\n    // TODO: remove this check in v6\n    if (!tip) {\n      return null\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n    // TODO: v6 the following can be achieved with CSS only\n    tip.classList.add(`bs-${this.constructor.NAME}-auto`)\n\n    const tipId = getUID(this.constructor.NAME).toString()\n\n    tip.setAttribute('id', tipId)\n\n    if (this._isAnimated()) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    return tip\n  }\n\n  setContent(content) {\n    this._newContent = content\n    if (this._isShown()) {\n      this._disposePopper()\n      this.show()\n    }\n  }\n\n  _getTemplateFactory(content) {\n    if (this._templateFactory) {\n      this._templateFactory.changeContent(content)\n    } else {\n      this._templateFactory = new TemplateFactory({\n        ...this._config,\n        // the `content` var has to be after `this._config`\n        // to override config.content in case of popover\n        content,\n        extraClass: this._resolvePossibleFunction(this._config.customClass)\n      })\n    }\n\n    return this._templateFactory\n  }\n\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TOOLTIP_INNER]: this._getTitle()\n    }\n  }\n\n  _getTitle() {\n    return this._resolvePossibleFunction(this._config.title) || this._element.getAttribute('data-bs-original-title')\n  }\n\n  // Private\n  _initializeOnDelegatedTarget(event) {\n    return this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig())\n  }\n\n  _isAnimated() {\n    return this._config.animation || (this.tip && this.tip.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _isShown() {\n    return this.tip && this.tip.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _createPopper(tip) {\n    const placement = execute(this._config.placement, [this, tip, this._element])\n    const attachment = AttachmentMap[placement.toUpperCase()]\n    return Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [this._element, this._element])\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'preSetPlacement',\n          enabled: true,\n          phase: 'beforeMain',\n          fn: data => {\n            // Pre-set Popper's placement attribute in order to read the arrow sizes properly.\n            // Otherwise, Popper mixes up the width and height dimensions since the initial arrow style is for top placement\n            this._getTipElement().setAttribute('data-popper-placement', data.state.placement)\n          }\n        }\n      ]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [undefined, defaultBsPopperConfig])\n    }\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    for (const trigger of triggers) {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.eventName(EVENT_CLICK), this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[TRIGGER_CLICK] = !(context._isShown() && context._activeTrigger[TRIGGER_CLICK])\n          context.toggle()\n        })\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSEENTER) :\n          this.constructor.eventName(EVENT_FOCUSIN)\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSELEAVE) :\n          this.constructor.eventName(EVENT_FOCUSOUT)\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER] = true\n          context._enter()\n        })\n        EventHandler.on(this._element, eventOut, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER] =\n            context._element.contains(event.relatedTarget)\n\n          context._leave()\n        })\n      }\n    }\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n\n    if (!title) {\n      return\n    }\n\n    if (!this._element.getAttribute('aria-label') && !this._element.textContent.trim()) {\n      this._element.setAttribute('aria-label', title)\n    }\n\n    this._element.setAttribute('data-bs-original-title', title) // DO NOT USE IT. Is only for backwards compatibility\n    this._element.removeAttribute('title')\n  }\n\n  _enter() {\n    if (this._isShown() || this._isHovered) {\n      this._isHovered = true\n      return\n    }\n\n    this._isHovered = true\n\n    this._setTimeout(() => {\n      if (this._isHovered) {\n        this.show()\n      }\n    }, this._config.delay.show)\n  }\n\n  _leave() {\n    if (this._isWithActiveTrigger()) {\n      return\n    }\n\n    this._isHovered = false\n\n    this._setTimeout(() => {\n      if (!this._isHovered) {\n        this.hide()\n      }\n    }, this._config.delay.hide)\n  }\n\n  _setTimeout(handler, timeout) {\n    clearTimeout(this._timeout)\n    this._timeout = setTimeout(handler, timeout)\n  }\n\n  _isWithActiveTrigger() {\n    return Object.values(this._activeTrigger).includes(true)\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    for (const dataAttribute of Object.keys(dataAttributes)) {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttribute)) {\n        delete dataAttributes[dataAttribute]\n      }\n    }\n\n    config = {\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    for (const [key, value] of Object.entries(this._config)) {\n      if (this.constructor.Default[key] !== value) {\n        config[key] = value\n      }\n    }\n\n    config.selector = false\n    config.trigger = 'manual'\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config\n  }\n\n  _disposePopper() {\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n\n    if (this.tip) {\n      this.tip.remove()\n      this.tip = null\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n"], "names": ["NAME", "DISALLOWED_ATTRIBUTES", "Set", "CLASS_NAME_FADE", "CLASS_NAME_MODAL", "CLASS_NAME_SHOW", "SELECTOR_TOOLTIP_INNER", "SELECTOR_MODAL", "EVENT_MODAL_HIDE", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_INSERTED", "EVENT_CLICK", "EVENT_FOCUSIN", "EVENT_FOCUSOUT", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "AttachmentMap", "AUTO", "TOP", "RIGHT", "isRTL", "BOTTOM", "LEFT", "<PERSON><PERSON><PERSON>", "allowList", "DefaultAllowlist", "animation", "boundary", "container", "customClass", "delay", "fallbackPlacements", "html", "offset", "placement", "popperConfig", "sanitize", "sanitizeFn", "selector", "template", "title", "trigger", "DefaultType", "<PERSON><PERSON><PERSON>", "BaseComponent", "constructor", "element", "config", "<PERSON><PERSON>", "TypeError", "_isEnabled", "_timeout", "_isHovered", "_activeTrigger", "_popper", "_templateFactory", "_newContent", "tip", "_setListeners", "_config", "_fixTitle", "enable", "disable", "toggle<PERSON>nabled", "toggle", "_isShown", "_leave", "_enter", "dispose", "clearTimeout", "EventHandler", "off", "_element", "closest", "_hideModalHandler", "getAttribute", "setAttribute", "_disposePopper", "show", "style", "display", "Error", "_isWithContent", "showEvent", "eventName", "shadowRoot", "findShadowRoot", "isInTheDom", "ownerDocument", "documentElement", "contains", "defaultPrevented", "_getTipElement", "append", "_createPopper", "classList", "add", "document", "concat", "body", "children", "on", "noop", "complete", "_queueCallback", "_isAnimated", "hide", "hideEvent", "remove", "_isWithActiveTrigger", "removeAttribute", "update", "Boolean", "_getTitle", "_createTipElement", "_getContentForTemplate", "content", "_getTemplateFactory", "toHtml", "tipId", "getUID", "toString", "<PERSON><PERSON><PERSON><PERSON>", "changeContent", "TemplateFactory", "extraClass", "_resolvePossibleFunction", "_initializeOnDelegatedTarget", "event", "getOrCreateInstance", "<PERSON><PERSON><PERSON><PERSON>", "_getDelegateConfig", "execute", "attachment", "toUpperCase", "createPopper", "_getPopperConfig", "_getOffset", "split", "map", "value", "Number", "parseInt", "popperData", "arg", "defaultBsPopperConfig", "modifiers", "name", "options", "enabled", "phase", "fn", "data", "state", "undefined", "triggers", "context", "eventIn", "eventOut", "type", "relatedTarget", "textContent", "trim", "_setTimeout", "handler", "timeout", "setTimeout", "Object", "values", "includes", "_getConfig", "dataAttributes", "Manipulator", "getDataAttributes", "dataAttribute", "keys", "has", "_mergeConfigObj", "_configAfterMerge", "_typeCheckConfig", "getElement", "key", "entries", "destroy", "jQueryInterface", "each", "defineJQueryPlugin"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;;EAYA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,SAAS;EACtB,MAAMC,qBAAqB,GAAG,IAAIC,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;EAE9E,MAAMC,eAAe,GAAG,MAAM;EAC9B,MAAMC,gBAAgB,GAAG,OAAO;EAChC,MAAMC,eAAe,GAAG,MAAM;EAE9B,MAAMC,sBAAsB,GAAG,gBAAgB;EAC/C,MAAMC,cAAc,GAAG,CAAA,CAAA,EAAIH,gBAAgB,CAAA,CAAE;EAE7C,MAAMI,gBAAgB,GAAG,eAAe;EAExC,MAAMC,aAAa,GAAG,OAAO;EAC7B,MAAMC,aAAa,GAAG,OAAO;EAC7B,MAAMC,aAAa,GAAG,OAAO;EAC7B,MAAMC,cAAc,GAAG,QAAQ;EAE/B,MAAMC,UAAU,GAAG,MAAM;EACzB,MAAMC,YAAY,GAAG,QAAQ;EAC7B,MAAMC,UAAU,GAAG,MAAM;EACzB,MAAMC,WAAW,GAAG,OAAO;EAC3B,MAAMC,cAAc,GAAG,UAAU;EACjC,MAAMC,WAAW,GAAG,OAAO;EAC3B,MAAMC,aAAa,GAAG,SAAS;EAC/B,MAAMC,cAAc,GAAG,UAAU;EACjC,MAAMC,gBAAgB,GAAG,YAAY;EACrC,MAAMC,gBAAgB,GAAG,YAAY;EAErC,MAAMC,aAAa,GAAG;EACpBC,EAAAA,IAAI,EAAE,MAAM;EACZC,EAAAA,GAAG,EAAE,KAAK;EACVC,EAAAA,KAAK,EAAEC,cAAK,EAAE,GAAG,MAAM,GAAG,OAAO;EACjCC,EAAAA,MAAM,EAAE,QAAQ;EAChBC,EAAAA,IAAI,EAAEF,cAAK,EAAE,GAAG,OAAO,GAAG;EAC5B,CAAC;EAED,MAAMG,OAAO,GAAG;EACdC,EAAAA,SAAS,EAAEC,6BAAgB;EAC3BC,EAAAA,SAAS,EAAE,IAAI;EACfC,EAAAA,QAAQ,EAAE,iBAAiB;EAC3BC,EAAAA,SAAS,EAAE,KAAK;EAChBC,EAAAA,WAAW,EAAE,EAAE;EACfC,EAAAA,KAAK,EAAE,CAAC;IACRC,kBAAkB,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;EACtDC,EAAAA,IAAI,EAAE,KAAK;EACXC,EAAAA,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACdC,EAAAA,SAAS,EAAE,KAAK;EAChBC,EAAAA,YAAY,EAAE,IAAI;EAClBC,EAAAA,QAAQ,EAAE,IAAI;EACdC,EAAAA,UAAU,EAAE,IAAI;EAChBC,EAAAA,QAAQ,EAAE,KAAK;EACfC,EAAAA,QAAQ,EAAE,sCAAsC,GACtC,mCAAmC,GACnC,mCAAmC,GACnC,QAAQ;EAClBC,EAAAA,KAAK,EAAE,EAAE;EACTC,EAAAA,OAAO,EAAE;EACX,CAAC;EAED,MAAMC,WAAW,GAAG;EAClBlB,EAAAA,SAAS,EAAE,QAAQ;EACnBE,EAAAA,SAAS,EAAE,SAAS;EACpBC,EAAAA,QAAQ,EAAE,kBAAkB;EAC5BC,EAAAA,SAAS,EAAE,0BAA0B;EACrCC,EAAAA,WAAW,EAAE,mBAAmB;EAChCC,EAAAA,KAAK,EAAE,iBAAiB;EACxBC,EAAAA,kBAAkB,EAAE,OAAO;EAC3BC,EAAAA,IAAI,EAAE,SAAS;EACfC,EAAAA,MAAM,EAAE,yBAAyB;EACjCC,EAAAA,SAAS,EAAE,mBAAmB;EAC9BC,EAAAA,YAAY,EAAE,wBAAwB;EACtCC,EAAAA,QAAQ,EAAE,SAAS;EACnBC,EAAAA,UAAU,EAAE,iBAAiB;EAC7BC,EAAAA,QAAQ,EAAE,kBAAkB;EAC5BC,EAAAA,QAAQ,EAAE,QAAQ;EAClBC,EAAAA,KAAK,EAAE,2BAA2B;EAClCC,EAAAA,OAAO,EAAE;EACX,CAAC;;EAED;EACA;EACA;;EAEA,MAAME,OAAO,SAASC,aAAa,CAAC;EAClCC,EAAAA,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAE;EAC3B,IAAA,IAAI,OAAOC,iBAAM,KAAK,WAAW,EAAE;EACjC,MAAA,MAAM,IAAIC,SAAS,CAAC,uEAAuE,CAAC;EAC9F,IAAA;EAEA,IAAA,KAAK,CAACH,OAAO,EAAEC,MAAM,CAAC;;EAEtB;MACA,IAAI,CAACG,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,QAAQ,GAAG,CAAC;MACjB,IAAI,CAACC,UAAU,GAAG,IAAI;EACtB,IAAA,IAAI,CAACC,cAAc,GAAG,EAAE;MACxB,IAAI,CAACC,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAACC,WAAW,GAAG,IAAI;;EAEvB;MACA,IAAI,CAACC,GAAG,GAAG,IAAI;MAEf,IAAI,CAACC,aAAa,EAAE;EAEpB,IAAA,IAAI,CAAC,IAAI,CAACC,OAAO,CAACrB,QAAQ,EAAE;QAC1B,IAAI,CAACsB,SAAS,EAAE;EAClB,IAAA;EACF,EAAA;;EAEA;IACA,WAAWrC,OAAOA,GAAG;EACnB,IAAA,OAAOA,OAAO;EAChB,EAAA;IAEA,WAAWmB,WAAWA,GAAG;EACvB,IAAA,OAAOA,WAAW;EACpB,EAAA;IAEA,WAAWjD,IAAIA,GAAG;EAChB,IAAA,OAAOA,IAAI;EACb,EAAA;;EAEA;EACAoE,EAAAA,MAAMA,GAAG;MACP,IAAI,CAACX,UAAU,GAAG,IAAI;EACxB,EAAA;EAEAY,EAAAA,OAAOA,GAAG;MACR,IAAI,CAACZ,UAAU,GAAG,KAAK;EACzB,EAAA;EAEAa,EAAAA,aAAaA,GAAG;EACd,IAAA,IAAI,CAACb,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC,EAAA;EAEAc,EAAAA,MAAMA,GAAG;EACP,IAAA,IAAI,CAAC,IAAI,CAACd,UAAU,EAAE;EACpB,MAAA;EACF,IAAA;EAEA,IAAA,IAAI,IAAI,CAACe,QAAQ,EAAE,EAAE;QACnB,IAAI,CAACC,MAAM,EAAE;EACb,MAAA;EACF,IAAA;MAEA,IAAI,CAACC,MAAM,EAAE;EACf,EAAA;EAEAC,EAAAA,OAAOA,GAAG;EACRC,IAAAA,YAAY,CAAC,IAAI,CAAClB,QAAQ,CAAC;EAE3BmB,IAAAA,YAAY,CAACC,GAAG,CAAC,IAAI,CAACC,QAAQ,CAACC,OAAO,CAACzE,cAAc,CAAC,EAAEC,gBAAgB,EAAE,IAAI,CAACyE,iBAAiB,CAAC;MAEjG,IAAI,IAAI,CAACF,QAAQ,CAACG,YAAY,CAAC,wBAAwB,CAAC,EAAE;EACxD,MAAA,IAAI,CAACH,QAAQ,CAACI,YAAY,CAAC,OAAO,EAAE,IAAI,CAACJ,QAAQ,CAACG,YAAY,CAAC,wBAAwB,CAAC,CAAC;EAC3F,IAAA;MAEA,IAAI,CAACE,cAAc,EAAE;MACrB,KAAK,CAACT,OAAO,EAAE;EACjB,EAAA;EAEAU,EAAAA,IAAIA,GAAG;MACL,IAAI,IAAI,CAACN,QAAQ,CAACO,KAAK,CAACC,OAAO,KAAK,MAAM,EAAE;EAC1C,MAAA,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;EACxD,IAAA;MAEA,IAAI,EAAE,IAAI,CAACC,cAAc,EAAE,IAAI,IAAI,CAAChC,UAAU,CAAC,EAAE;EAC/C,MAAA;EACF,IAAA;EAEA,IAAA,MAAMiC,SAAS,GAAGb,YAAY,CAAC7B,OAAO,CAAC,IAAI,CAAC+B,QAAQ,EAAE,IAAI,CAAC3B,WAAW,CAACuC,SAAS,CAAC5E,UAAU,CAAC,CAAC;EAC7F,IAAA,MAAM6E,UAAU,GAAGC,uBAAc,CAAC,IAAI,CAACd,QAAQ,CAAC;EAChD,IAAA,MAAMe,UAAU,GAAG,CAACF,UAAU,IAAI,IAAI,CAACb,QAAQ,CAACgB,aAAa,CAACC,eAAe,EAAEC,QAAQ,CAAC,IAAI,CAAClB,QAAQ,CAAC;EAEtG,IAAA,IAAIW,SAAS,CAACQ,gBAAgB,IAAI,CAACJ,UAAU,EAAE;EAC7C,MAAA;EACF,IAAA;;EAEA;MACA,IAAI,CAACV,cAAc,EAAE;EAErB,IAAA,MAAMpB,GAAG,GAAG,IAAI,CAACmC,cAAc,EAAE;EAEjC,IAAA,IAAI,CAACpB,QAAQ,CAACI,YAAY,CAAC,kBAAkB,EAAEnB,GAAG,CAACkB,YAAY,CAAC,IAAI,CAAC,CAAC;MAEtE,MAAM;EAAE/C,MAAAA;OAAW,GAAG,IAAI,CAAC+B,OAAO;EAElC,IAAA,IAAI,CAAC,IAAI,CAACa,QAAQ,CAACgB,aAAa,CAACC,eAAe,CAACC,QAAQ,CAAC,IAAI,CAACjC,GAAG,CAAC,EAAE;EACnE7B,MAAAA,SAAS,CAACiE,MAAM,CAACpC,GAAG,CAAC;EACrBa,MAAAA,YAAY,CAAC7B,OAAO,CAAC,IAAI,CAAC+B,QAAQ,EAAE,IAAI,CAAC3B,WAAW,CAACuC,SAAS,CAAC1E,cAAc,CAAC,CAAC;EACjF,IAAA;MAEA,IAAI,CAAC4C,OAAO,GAAG,IAAI,CAACwC,aAAa,CAACrC,GAAG,CAAC;EAEtCA,IAAAA,GAAG,CAACsC,SAAS,CAACC,GAAG,CAAClG,eAAe,CAAC;;EAElC;EACA;EACA;EACA;EACA,IAAA,IAAI,cAAc,IAAImG,QAAQ,CAACR,eAAe,EAAE;EAC9C,MAAA,KAAK,MAAM3C,OAAO,IAAI,EAAE,CAACoD,MAAM,CAAC,GAAGD,QAAQ,CAACE,IAAI,CAACC,QAAQ,CAAC,EAAE;UAC1D9B,YAAY,CAAC+B,EAAE,CAACvD,OAAO,EAAE,WAAW,EAAEwD,aAAI,CAAC;EAC7C,MAAA;EACF,IAAA;MAEA,MAAMC,QAAQ,GAAGA,MAAM;EACrBjC,MAAAA,YAAY,CAAC7B,OAAO,CAAC,IAAI,CAAC+B,QAAQ,EAAE,IAAI,CAAC3B,WAAW,CAACuC,SAAS,CAAC3E,WAAW,CAAC,CAAC;EAE5E,MAAA,IAAI,IAAI,CAAC2C,UAAU,KAAK,KAAK,EAAE;UAC7B,IAAI,CAACc,MAAM,EAAE;EACf,MAAA;QAEA,IAAI,CAACd,UAAU,GAAG,KAAK;MACzB,CAAC;EAED,IAAA,IAAI,CAACoD,cAAc,CAACD,QAAQ,EAAE,IAAI,CAAC9C,GAAG,EAAE,IAAI,CAACgD,WAAW,EAAE,CAAC;EAC7D,EAAA;EAEAC,EAAAA,IAAIA,GAAG;EACL,IAAA,IAAI,CAAC,IAAI,CAACzC,QAAQ,EAAE,EAAE;EACpB,MAAA;EACF,IAAA;EAEA,IAAA,MAAM0C,SAAS,GAAGrC,YAAY,CAAC7B,OAAO,CAAC,IAAI,CAAC+B,QAAQ,EAAE,IAAI,CAAC3B,WAAW,CAACuC,SAAS,CAAC9E,UAAU,CAAC,CAAC;MAC7F,IAAIqG,SAAS,CAAChB,gBAAgB,EAAE;EAC9B,MAAA;EACF,IAAA;EAEA,IAAA,MAAMlC,GAAG,GAAG,IAAI,CAACmC,cAAc,EAAE;EACjCnC,IAAAA,GAAG,CAACsC,SAAS,CAACa,MAAM,CAAC9G,eAAe,CAAC;;EAErC;EACA;EACA,IAAA,IAAI,cAAc,IAAImG,QAAQ,CAACR,eAAe,EAAE;EAC9C,MAAA,KAAK,MAAM3C,OAAO,IAAI,EAAE,CAACoD,MAAM,CAAC,GAAGD,QAAQ,CAACE,IAAI,CAACC,QAAQ,CAAC,EAAE;UAC1D9B,YAAY,CAACC,GAAG,CAACzB,OAAO,EAAE,WAAW,EAAEwD,aAAI,CAAC;EAC9C,MAAA;EACF,IAAA;EAEA,IAAA,IAAI,CAACjD,cAAc,CAACjD,aAAa,CAAC,GAAG,KAAK;EAC1C,IAAA,IAAI,CAACiD,cAAc,CAAClD,aAAa,CAAC,GAAG,KAAK;EAC1C,IAAA,IAAI,CAACkD,cAAc,CAACnD,aAAa,CAAC,GAAG,KAAK;EAC1C,IAAA,IAAI,CAACkD,UAAU,GAAG,IAAI,CAAA;;MAEtB,MAAMmD,QAAQ,GAAGA,MAAM;EACrB,MAAA,IAAI,IAAI,CAACM,oBAAoB,EAAE,EAAE;EAC/B,QAAA;EACF,MAAA;EAEA,MAAA,IAAI,CAAC,IAAI,CAACzD,UAAU,EAAE;UACpB,IAAI,CAACyB,cAAc,EAAE;EACvB,MAAA;EAEA,MAAA,IAAI,CAACL,QAAQ,CAACsC,eAAe,CAAC,kBAAkB,CAAC;EACjDxC,MAAAA,YAAY,CAAC7B,OAAO,CAAC,IAAI,CAAC+B,QAAQ,EAAE,IAAI,CAAC3B,WAAW,CAACuC,SAAS,CAAC7E,YAAY,CAAC,CAAC;MAC/E,CAAC;EAED,IAAA,IAAI,CAACiG,cAAc,CAACD,QAAQ,EAAE,IAAI,CAAC9C,GAAG,EAAE,IAAI,CAACgD,WAAW,EAAE,CAAC;EAC7D,EAAA;EAEAM,EAAAA,MAAMA,GAAG;MACP,IAAI,IAAI,CAACzD,OAAO,EAAE;EAChB,MAAA,IAAI,CAACA,OAAO,CAACyD,MAAM,EAAE;EACvB,IAAA;EACF,EAAA;;EAEA;EACA7B,EAAAA,cAAcA,GAAG;EACf,IAAA,OAAO8B,OAAO,CAAC,IAAI,CAACC,SAAS,EAAE,CAAC;EAClC,EAAA;EAEArB,EAAAA,cAAcA,GAAG;EACf,IAAA,IAAI,CAAC,IAAI,CAACnC,GAAG,EAAE;EACb,MAAA,IAAI,CAACA,GAAG,GAAG,IAAI,CAACyD,iBAAiB,CAAC,IAAI,CAAC1D,WAAW,IAAI,IAAI,CAAC2D,sBAAsB,EAAE,CAAC;EACtF,IAAA;MAEA,OAAO,IAAI,CAAC1D,GAAG;EACjB,EAAA;IAEAyD,iBAAiBA,CAACE,OAAO,EAAE;MACzB,MAAM3D,GAAG,GAAG,IAAI,CAAC4D,mBAAmB,CAACD,OAAO,CAAC,CAACE,MAAM,EAAE;;EAEtD;MACA,IAAI,CAAC7D,GAAG,EAAE;EACR,MAAA,OAAO,IAAI;EACb,IAAA;MAEAA,GAAG,CAACsC,SAAS,CAACa,MAAM,CAAChH,eAAe,EAAEE,eAAe,CAAC;EACtD;EACA2D,IAAAA,GAAG,CAACsC,SAAS,CAACC,GAAG,CAAC,CAAA,GAAA,EAAM,IAAI,CAACnD,WAAW,CAACpD,IAAI,CAAA,KAAA,CAAO,CAAC;EAErD,IAAA,MAAM8H,KAAK,GAAGC,eAAM,CAAC,IAAI,CAAC3E,WAAW,CAACpD,IAAI,CAAC,CAACgI,QAAQ,EAAE;EAEtDhE,IAAAA,GAAG,CAACmB,YAAY,CAAC,IAAI,EAAE2C,KAAK,CAAC;EAE7B,IAAA,IAAI,IAAI,CAACd,WAAW,EAAE,EAAE;EACtBhD,MAAAA,GAAG,CAACsC,SAAS,CAACC,GAAG,CAACpG,eAAe,CAAC;EACpC,IAAA;EAEA,IAAA,OAAO6D,GAAG;EACZ,EAAA;IAEAiE,UAAUA,CAACN,OAAO,EAAE;MAClB,IAAI,CAAC5D,WAAW,GAAG4D,OAAO;EAC1B,IAAA,IAAI,IAAI,CAACnD,QAAQ,EAAE,EAAE;QACnB,IAAI,CAACY,cAAc,EAAE;QACrB,IAAI,CAACC,IAAI,EAAE;EACb,IAAA;EACF,EAAA;IAEAuC,mBAAmBA,CAACD,OAAO,EAAE;MAC3B,IAAI,IAAI,CAAC7D,gBAAgB,EAAE;EACzB,MAAA,IAAI,CAACA,gBAAgB,CAACoE,aAAa,CAACP,OAAO,CAAC;EAC9C,IAAA,CAAC,MAAM;EACL,MAAA,IAAI,CAAC7D,gBAAgB,GAAG,IAAIqE,eAAe,CAAC;UAC1C,GAAG,IAAI,CAACjE,OAAO;EACf;EACA;UACAyD,OAAO;UACPS,UAAU,EAAE,IAAI,CAACC,wBAAwB,CAAC,IAAI,CAACnE,OAAO,CAAC9B,WAAW;EACpE,OAAC,CAAC;EACJ,IAAA;MAEA,OAAO,IAAI,CAAC0B,gBAAgB;EAC9B,EAAA;EAEA4D,EAAAA,sBAAsBA,GAAG;MACvB,OAAO;EACL,MAAA,CAACpH,sBAAsB,GAAG,IAAI,CAACkH,SAAS;OACzC;EACH,EAAA;EAEAA,EAAAA,SAASA,GAAG;EACV,IAAA,OAAO,IAAI,CAACa,wBAAwB,CAAC,IAAI,CAACnE,OAAO,CAACnB,KAAK,CAAC,IAAI,IAAI,CAACgC,QAAQ,CAACG,YAAY,CAAC,wBAAwB,CAAC;EAClH,EAAA;;EAEA;IACAoD,4BAA4BA,CAACC,KAAK,EAAE;EAClC,IAAA,OAAO,IAAI,CAACnF,WAAW,CAACoF,mBAAmB,CAACD,KAAK,CAACE,cAAc,EAAE,IAAI,CAACC,kBAAkB,EAAE,CAAC;EAC9F,EAAA;EAEA1B,EAAAA,WAAWA,GAAG;EACZ,IAAA,OAAO,IAAI,CAAC9C,OAAO,CAACjC,SAAS,IAAK,IAAI,CAAC+B,GAAG,IAAI,IAAI,CAACA,GAAG,CAACsC,SAAS,CAACL,QAAQ,CAAC9F,eAAe,CAAE;EAC7F,EAAA;EAEAqE,EAAAA,QAAQA,GAAG;EACT,IAAA,OAAO,IAAI,CAACR,GAAG,IAAI,IAAI,CAACA,GAAG,CAACsC,SAAS,CAACL,QAAQ,CAAC5F,eAAe,CAAC;EACjE,EAAA;IAEAgG,aAAaA,CAACrC,GAAG,EAAE;EACjB,IAAA,MAAMvB,SAAS,GAAGkG,gBAAO,CAAC,IAAI,CAACzE,OAAO,CAACzB,SAAS,EAAE,CAAC,IAAI,EAAEuB,GAAG,EAAE,IAAI,CAACe,QAAQ,CAAC,CAAC;MAC7E,MAAM6D,UAAU,GAAGrH,aAAa,CAACkB,SAAS,CAACoG,WAAW,EAAE,CAAC;EACzD,IAAA,OAAOtF,iBAAM,CAACuF,YAAY,CAAC,IAAI,CAAC/D,QAAQ,EAAEf,GAAG,EAAE,IAAI,CAAC+E,gBAAgB,CAACH,UAAU,CAAC,CAAC;EACnF,EAAA;EAEAI,EAAAA,UAAUA,GAAG;MACX,MAAM;EAAExG,MAAAA;OAAQ,GAAG,IAAI,CAAC0B,OAAO;EAE/B,IAAA,IAAI,OAAO1B,MAAM,KAAK,QAAQ,EAAE;EAC9B,MAAA,OAAOA,MAAM,CAACyG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,KAAK,IAAIC,MAAM,CAACC,QAAQ,CAACF,KAAK,EAAE,EAAE,CAAC,CAAC;EACnE,IAAA;EAEA,IAAA,IAAI,OAAO3G,MAAM,KAAK,UAAU,EAAE;QAChC,OAAO8G,UAAU,IAAI9G,MAAM,CAAC8G,UAAU,EAAE,IAAI,CAACvE,QAAQ,CAAC;EACxD,IAAA;EAEA,IAAA,OAAOvC,MAAM;EACf,EAAA;IAEA6F,wBAAwBA,CAACkB,GAAG,EAAE;EAC5B,IAAA,OAAOZ,gBAAO,CAACY,GAAG,EAAE,CAAC,IAAI,CAACxE,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC,CAAC;EACrD,EAAA;IAEAgE,gBAAgBA,CAACH,UAAU,EAAE;EAC3B,IAAA,MAAMY,qBAAqB,GAAG;EAC5B/G,MAAAA,SAAS,EAAEmG,UAAU;EACrBa,MAAAA,SAAS,EAAE,CACT;EACEC,QAAAA,IAAI,EAAE,MAAM;EACZC,QAAAA,OAAO,EAAE;EACPrH,UAAAA,kBAAkB,EAAE,IAAI,CAAC4B,OAAO,CAAC5B;EACnC;EACF,OAAC,EACD;EACEoH,QAAAA,IAAI,EAAE,QAAQ;EACdC,QAAAA,OAAO,EAAE;EACPnH,UAAAA,MAAM,EAAE,IAAI,CAACwG,UAAU;EACzB;EACF,OAAC,EACD;EACEU,QAAAA,IAAI,EAAE,iBAAiB;EACvBC,QAAAA,OAAO,EAAE;EACPzH,UAAAA,QAAQ,EAAE,IAAI,CAACgC,OAAO,CAAChC;EACzB;EACF,OAAC,EACD;EACEwH,QAAAA,IAAI,EAAE,OAAO;EACbC,QAAAA,OAAO,EAAE;EACPtG,UAAAA,OAAO,EAAE,CAAA,CAAA,EAAI,IAAI,CAACD,WAAW,CAACpD,IAAI,CAAA,MAAA;EACpC;EACF,OAAC,EACD;EACE0J,QAAAA,IAAI,EAAE,iBAAiB;EACvBE,QAAAA,OAAO,EAAE,IAAI;EACbC,QAAAA,KAAK,EAAE,YAAY;UACnBC,EAAE,EAAEC,IAAI,IAAI;EACV;EACA;EACA,UAAA,IAAI,CAAC5D,cAAc,EAAE,CAAChB,YAAY,CAAC,uBAAuB,EAAE4E,IAAI,CAACC,KAAK,CAACvH,SAAS,CAAC;EACnF,QAAA;SACD;OAEJ;MAED,OAAO;EACL,MAAA,GAAG+G,qBAAqB;EACxB,MAAA,GAAGb,gBAAO,CAAC,IAAI,CAACzE,OAAO,CAACxB,YAAY,EAAE,CAACuH,SAAS,EAAET,qBAAqB,CAAC;OACzE;EACH,EAAA;EAEAvF,EAAAA,aAAaA,GAAG;MACd,MAAMiG,QAAQ,GAAG,IAAI,CAAChG,OAAO,CAAClB,OAAO,CAACiG,KAAK,CAAC,GAAG,CAAC;EAEhD,IAAA,KAAK,MAAMjG,OAAO,IAAIkH,QAAQ,EAAE;QAC9B,IAAIlH,OAAO,KAAK,OAAO,EAAE;UACvB6B,YAAY,CAAC+B,EAAE,CAAC,IAAI,CAAC7B,QAAQ,EAAE,IAAI,CAAC3B,WAAW,CAACuC,SAAS,CAACzE,WAAW,CAAC,EAAE,IAAI,CAACgD,OAAO,CAACrB,QAAQ,EAAE0F,KAAK,IAAI;EACtG,UAAA,MAAM4B,OAAO,GAAG,IAAI,CAAC7B,4BAA4B,CAACC,KAAK,CAAC;EACxD4B,UAAAA,OAAO,CAACvG,cAAc,CAACjD,aAAa,CAAC,GAAG,EAAEwJ,OAAO,CAAC3F,QAAQ,EAAE,IAAI2F,OAAO,CAACvG,cAAc,CAACjD,aAAa,CAAC,CAAC;YACtGwJ,OAAO,CAAC5F,MAAM,EAAE;EAClB,QAAA,CAAC,CAAC;EACJ,MAAA,CAAC,MAAM,IAAIvB,OAAO,KAAKpC,cAAc,EAAE;UACrC,MAAMwJ,OAAO,GAAGpH,OAAO,KAAKvC,aAAa,GACvC,IAAI,CAAC2C,WAAW,CAACuC,SAAS,CAACtE,gBAAgB,CAAC,GAC5C,IAAI,CAAC+B,WAAW,CAACuC,SAAS,CAACxE,aAAa,CAAC;UAC3C,MAAMkJ,QAAQ,GAAGrH,OAAO,KAAKvC,aAAa,GACxC,IAAI,CAAC2C,WAAW,CAACuC,SAAS,CAACrE,gBAAgB,CAAC,GAC5C,IAAI,CAAC8B,WAAW,CAACuC,SAAS,CAACvE,cAAc,CAAC;EAE5CyD,QAAAA,YAAY,CAAC+B,EAAE,CAAC,IAAI,CAAC7B,QAAQ,EAAEqF,OAAO,EAAE,IAAI,CAAClG,OAAO,CAACrB,QAAQ,EAAE0F,KAAK,IAAI;EACtE,UAAA,MAAM4B,OAAO,GAAG,IAAI,CAAC7B,4BAA4B,CAACC,KAAK,CAAC;EACxD4B,UAAAA,OAAO,CAACvG,cAAc,CAAC2E,KAAK,CAAC+B,IAAI,KAAK,SAAS,GAAG5J,aAAa,GAAGD,aAAa,CAAC,GAAG,IAAI;YACvF0J,OAAO,CAACzF,MAAM,EAAE;EAClB,QAAA,CAAC,CAAC;EACFG,QAAAA,YAAY,CAAC+B,EAAE,CAAC,IAAI,CAAC7B,QAAQ,EAAEsF,QAAQ,EAAE,IAAI,CAACnG,OAAO,CAACrB,QAAQ,EAAE0F,KAAK,IAAI;EACvE,UAAA,MAAM4B,OAAO,GAAG,IAAI,CAAC7B,4BAA4B,CAACC,KAAK,CAAC;YACxD4B,OAAO,CAACvG,cAAc,CAAC2E,KAAK,CAAC+B,IAAI,KAAK,UAAU,GAAG5J,aAAa,GAAGD,aAAa,CAAC,GAC/E0J,OAAO,CAACpF,QAAQ,CAACkB,QAAQ,CAACsC,KAAK,CAACgC,aAAa,CAAC;YAEhDJ,OAAO,CAAC1F,MAAM,EAAE;EAClB,QAAA,CAAC,CAAC;EACJ,MAAA;EACF,IAAA;MAEA,IAAI,CAACQ,iBAAiB,GAAG,MAAM;QAC7B,IAAI,IAAI,CAACF,QAAQ,EAAE;UACjB,IAAI,CAACkC,IAAI,EAAE;EACb,MAAA;MACF,CAAC;EAEDpC,IAAAA,YAAY,CAAC+B,EAAE,CAAC,IAAI,CAAC7B,QAAQ,CAACC,OAAO,CAACzE,cAAc,CAAC,EAAEC,gBAAgB,EAAE,IAAI,CAACyE,iBAAiB,CAAC;EAClG,EAAA;EAEAd,EAAAA,SAASA,GAAG;MACV,MAAMpB,KAAK,GAAG,IAAI,CAACgC,QAAQ,CAACG,YAAY,CAAC,OAAO,CAAC;MAEjD,IAAI,CAACnC,KAAK,EAAE;EACV,MAAA;EACF,IAAA;MAEA,IAAI,CAAC,IAAI,CAACgC,QAAQ,CAACG,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAACH,QAAQ,CAACyF,WAAW,CAACC,IAAI,EAAE,EAAE;QAClF,IAAI,CAAC1F,QAAQ,CAACI,YAAY,CAAC,YAAY,EAAEpC,KAAK,CAAC;EACjD,IAAA;MAEA,IAAI,CAACgC,QAAQ,CAACI,YAAY,CAAC,wBAAwB,EAAEpC,KAAK,CAAC,CAAA;EAC3D,IAAA,IAAI,CAACgC,QAAQ,CAACsC,eAAe,CAAC,OAAO,CAAC;EACxC,EAAA;EAEA3C,EAAAA,MAAMA,GAAG;MACP,IAAI,IAAI,CAACF,QAAQ,EAAE,IAAI,IAAI,CAACb,UAAU,EAAE;QACtC,IAAI,CAACA,UAAU,GAAG,IAAI;EACtB,MAAA;EACF,IAAA;MAEA,IAAI,CAACA,UAAU,GAAG,IAAI;MAEtB,IAAI,CAAC+G,WAAW,CAAC,MAAM;QACrB,IAAI,IAAI,CAAC/G,UAAU,EAAE;UACnB,IAAI,CAAC0B,IAAI,EAAE;EACb,MAAA;MACF,CAAC,EAAE,IAAI,CAACnB,OAAO,CAAC7B,KAAK,CAACgD,IAAI,CAAC;EAC7B,EAAA;EAEAZ,EAAAA,MAAMA,GAAG;EACP,IAAA,IAAI,IAAI,CAAC2C,oBAAoB,EAAE,EAAE;EAC/B,MAAA;EACF,IAAA;MAEA,IAAI,CAACzD,UAAU,GAAG,KAAK;MAEvB,IAAI,CAAC+G,WAAW,CAAC,MAAM;EACrB,MAAA,IAAI,CAAC,IAAI,CAAC/G,UAAU,EAAE;UACpB,IAAI,CAACsD,IAAI,EAAE;EACb,MAAA;MACF,CAAC,EAAE,IAAI,CAAC/C,OAAO,CAAC7B,KAAK,CAAC4E,IAAI,CAAC;EAC7B,EAAA;EAEAyD,EAAAA,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAE;EAC5BhG,IAAAA,YAAY,CAAC,IAAI,CAAClB,QAAQ,CAAC;MAC3B,IAAI,CAACA,QAAQ,GAAGmH,UAAU,CAACF,OAAO,EAAEC,OAAO,CAAC;EAC9C,EAAA;EAEAxD,EAAAA,oBAAoBA,GAAG;EACrB,IAAA,OAAO0D,MAAM,CAACC,MAAM,CAAC,IAAI,CAACnH,cAAc,CAAC,CAACoH,QAAQ,CAAC,IAAI,CAAC;EAC1D,EAAA;IAEAC,UAAUA,CAAC3H,MAAM,EAAE;MACjB,MAAM4H,cAAc,GAAGC,WAAW,CAACC,iBAAiB,CAAC,IAAI,CAACrG,QAAQ,CAAC;MAEnE,KAAK,MAAMsG,aAAa,IAAIP,MAAM,CAACQ,IAAI,CAACJ,cAAc,CAAC,EAAE;EACvD,MAAA,IAAIjL,qBAAqB,CAACsL,GAAG,CAACF,aAAa,CAAC,EAAE;UAC5C,OAAOH,cAAc,CAACG,aAAa,CAAC;EACtC,MAAA;EACF,IAAA;EAEA/H,IAAAA,MAAM,GAAG;EACP,MAAA,GAAG4H,cAAc;QACjB,IAAI,OAAO5H,MAAM,KAAK,QAAQ,IAAIA,MAAM,GAAGA,MAAM,GAAG,EAAE;OACvD;EACDA,IAAAA,MAAM,GAAG,IAAI,CAACkI,eAAe,CAAClI,MAAM,CAAC;EACrCA,IAAAA,MAAM,GAAG,IAAI,CAACmI,iBAAiB,CAACnI,MAAM,CAAC;EACvC,IAAA,IAAI,CAACoI,gBAAgB,CAACpI,MAAM,CAAC;EAC7B,IAAA,OAAOA,MAAM;EACf,EAAA;IAEAmI,iBAAiBA,CAACnI,MAAM,EAAE;EACxBA,IAAAA,MAAM,CAACnB,SAAS,GAAGmB,MAAM,CAACnB,SAAS,KAAK,KAAK,GAAGqE,QAAQ,CAACE,IAAI,GAAGiF,mBAAU,CAACrI,MAAM,CAACnB,SAAS,CAAC;EAE5F,IAAA,IAAI,OAAOmB,MAAM,CAACjB,KAAK,KAAK,QAAQ,EAAE;QACpCiB,MAAM,CAACjB,KAAK,GAAG;UACbgD,IAAI,EAAE/B,MAAM,CAACjB,KAAK;UAClB4E,IAAI,EAAE3D,MAAM,CAACjB;SACd;EACH,IAAA;EAEA,IAAA,IAAI,OAAOiB,MAAM,CAACP,KAAK,KAAK,QAAQ,EAAE;QACpCO,MAAM,CAACP,KAAK,GAAGO,MAAM,CAACP,KAAK,CAACiF,QAAQ,EAAE;EACxC,IAAA;EAEA,IAAA,IAAI,OAAO1E,MAAM,CAACqE,OAAO,KAAK,QAAQ,EAAE;QACtCrE,MAAM,CAACqE,OAAO,GAAGrE,MAAM,CAACqE,OAAO,CAACK,QAAQ,EAAE;EAC5C,IAAA;EAEA,IAAA,OAAO1E,MAAM;EACf,EAAA;EAEAoF,EAAAA,kBAAkBA,GAAG;MACnB,MAAMpF,MAAM,GAAG,EAAE;EAEjB,IAAA,KAAK,MAAM,CAACsI,GAAG,EAAEzC,KAAK,CAAC,IAAI2B,MAAM,CAACe,OAAO,CAAC,IAAI,CAAC3H,OAAO,CAAC,EAAE;QACvD,IAAI,IAAI,CAACd,WAAW,CAACtB,OAAO,CAAC8J,GAAG,CAAC,KAAKzC,KAAK,EAAE;EAC3C7F,QAAAA,MAAM,CAACsI,GAAG,CAAC,GAAGzC,KAAK;EACrB,MAAA;EACF,IAAA;MAEA7F,MAAM,CAACT,QAAQ,GAAG,KAAK;MACvBS,MAAM,CAACN,OAAO,GAAG,QAAQ;;EAEzB;EACA;EACA;EACA,IAAA,OAAOM,MAAM;EACf,EAAA;EAEA8B,EAAAA,cAAcA,GAAG;MACf,IAAI,IAAI,CAACvB,OAAO,EAAE;EAChB,MAAA,IAAI,CAACA,OAAO,CAACiI,OAAO,EAAE;QACtB,IAAI,CAACjI,OAAO,GAAG,IAAI;EACrB,IAAA;MAEA,IAAI,IAAI,CAACG,GAAG,EAAE;EACZ,MAAA,IAAI,CAACA,GAAG,CAACmD,MAAM,EAAE;QACjB,IAAI,CAACnD,GAAG,GAAG,IAAI;EACjB,IAAA;EACF,EAAA;;EAEA;IACA,OAAO+H,eAAeA,CAACzI,MAAM,EAAE;EAC7B,IAAA,OAAO,IAAI,CAAC0I,IAAI,CAAC,YAAY;QAC3B,MAAMjC,IAAI,GAAG7G,OAAO,CAACsF,mBAAmB,CAAC,IAAI,EAAElF,MAAM,CAAC;EAEtD,MAAA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;EAC9B,QAAA;EACF,MAAA;EAEA,MAAA,IAAI,OAAOyG,IAAI,CAACzG,MAAM,CAAC,KAAK,WAAW,EAAE;EACvC,QAAA,MAAM,IAAIE,SAAS,CAAC,CAAA,iBAAA,EAAoBF,MAAM,GAAG,CAAC;EACpD,MAAA;EAEAyG,MAAAA,IAAI,CAACzG,MAAM,CAAC,EAAE;EAChB,IAAA,CAAC,CAAC;EACJ,EAAA;EACF;;EAEA;EACA;EACA;;AAEA2I,6BAAkB,CAAC/I,OAAO,CAAC;;;;;;;;"}