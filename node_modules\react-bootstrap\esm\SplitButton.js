import * as React from 'react';
import PropTypes from 'prop-types';
import Button from './Button';
import ButtonGroup from './ButtonGroup';
import Dropdown from './Dropdown';
import { alignPropType } from './types';
import { jsx as _jsx } from "react/jsx-runtime";
import { jsxs as _jsxs } from "react/jsx-runtime";
const propTypes = {
  /**
   * An html id attribute for the Toggle button, necessary for assistive technologies, such as screen readers.
   * @type {string}
   * @required
   */
  id: PropTypes.string,
  /**
   * Accessible label for the toggle; the value of `title` if not specified.
   */
  toggleLabel: PropTypes.string,
  /** An `href` passed to the non-toggle Button */
  href: PropTypes.string,
  /** An anchor `target` passed to the non-toggle Button */
  target: PropTypes.string,
  /** An `onClick` handler passed to the non-toggle Button */
  onClick: PropTypes.func,
  /** The content of the non-toggle Button.  */
  title: PropTypes.node.isRequired,
  /** A `type` passed to the non-toggle Button */
  type: PropTypes.string,
  /** Disables both Buttons  */
  disabled: PropTypes.bool,
  /**
   * Aligns the dropdown menu.
   *
   * _see [DropdownMenu](#dropdown-menu-props) for more details_
   *
   * @type {"start"|"end"|{ sm: "start"|"end" }|{ md: "start"|"end" }|{ lg: "start"|"end" }|{ xl: "start"|"end"}|{ xxl: "start"|"end"} }
   */
  align: alignPropType,
  /** An ARIA accessible role applied to the Menu component. When set to 'menu', The dropdown */
  menuRole: PropTypes.string,
  /** Whether to render the dropdown menu in the DOM before the first time it is shown */
  renderMenuOnMount: PropTypes.bool,
  /**
   *  Which event when fired outside the component will cause it to be closed.
   *
   * _see [DropdownMenu](#dropdown-menu-props) for more details_
   */
  rootCloseEvent: PropTypes.string,
  /**
   * Allow Dropdown to flip in case of an overlapping on the reference element. For more information refer to
   * Popper.js's flip [docs](https://popper.js.org/docs/v2/modifiers/flip/).
   *
   */
  flip: PropTypes.bool,
  /** @ignore */
  bsPrefix: PropTypes.string,
  /** @ignore */
  variant: PropTypes.string,
  /** @ignore */
  size: PropTypes.string
};

/**
 * A convenience component for simple or general use split button dropdowns. Renders a
 * `ButtonGroup` containing a `Button` and a `Button` toggle for the `Dropdown`. All `children`
 * are passed directly to the default `Dropdown.Menu`. This component accepts all of [`Dropdown`'s
 * props](#dropdown-props).
 *
 * _All unknown props are passed through to the `Dropdown` component._
 * The Button `variant`, `size` and `bsPrefix` props are passed to the button and toggle,
 * and menu-related props are passed to the `Dropdown.Menu`
 */
const SplitButton = /*#__PURE__*/React.forwardRef(({
  id,
  bsPrefix,
  size,
  variant,
  title,
  type = 'button',
  toggleLabel = 'Toggle dropdown',
  children,
  onClick,
  href,
  target,
  menuRole,
  renderMenuOnMount,
  rootCloseEvent,
  flip,
  ...props
}, ref) => /*#__PURE__*/_jsxs(Dropdown, {
  ref: ref,
  ...props,
  as: ButtonGroup,
  children: [/*#__PURE__*/_jsx(Button, {
    size: size,
    variant: variant,
    disabled: props.disabled,
    bsPrefix: bsPrefix,
    href: href,
    target: target,
    onClick: onClick,
    type: type,
    children: title
  }), /*#__PURE__*/_jsx(Dropdown.Toggle, {
    split: true,
    id: id,
    size: size,
    variant: variant,
    disabled: props.disabled,
    childBsPrefix: bsPrefix,
    children: /*#__PURE__*/_jsx("span", {
      className: "visually-hidden",
      children: toggleLabel
    })
  }), /*#__PURE__*/_jsx(Dropdown.Menu, {
    role: menuRole,
    renderOnMount: renderMenuOnMount,
    rootCloseEvent: rootCloseEvent,
    flip: flip,
    children: children
  })]
}));
SplitButton.propTypes = propTypes;
SplitButton.displayName = 'SplitButton';
export default SplitButton;