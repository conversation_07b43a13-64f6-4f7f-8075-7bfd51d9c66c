* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body, html {
  margin: 0;
  padding: 0;
  height: 100%;
}

.app {
  width: 100vw;
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  box-sizing: border-box;
}

.products-container {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.product-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  flex: 1;
  min-width: 200px;
}

.product-image {
  font-size: 4rem;
  margin-bottom: 0.5rem;
}

.product-info h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
}

.product-info p {
  margin: 0.5rem 0;
  font-size: 1.1rem;
  color: #666;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 1rem 0;
}

.quantity-btn {
  width: 30px;
  height: 30px;
  border: none;
  border-radius: 50%;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn:first-child {
  background-color: #28a745;
  color: white;
}

.quantity-btn:last-child {
  background-color: #007bff;
  color: white;
}

.quantity-btn:hover {
  opacity: 0.8;
}

.quantity {
  font-size: 1.2rem;
  font-weight: bold;
  min-width: 20px;
}

.delete-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
}

.delete-btn:hover {
  background-color: #c82333;
}

.total {
  text-align: center;
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  margin-top: 2rem;
  padding: 1rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
