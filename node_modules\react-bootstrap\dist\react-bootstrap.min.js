/*! For license information please see react-bootstrap.min.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react"),require("react-dom")):"function"==typeof define&&define.amd?define(["react","react-dom"],t):"object"==typeof exports?exports.ReactBootstrap=t(require("react"),require("react-dom")):e.<PERSON>actBootstrap=t(e.<PERSON><PERSON>,e.ReactDOM)}(self,((e,t)=>(()=>{var n={737:e=>{"use strict";e.exports=function(e,t,n,r,o,a,s,i){if(!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[n,r,o,a,s,i],u=0;(l=new Error(t.replace(/%s/g,(function(){return c[u++]})))).name="Invariant Violation"}throw l.framesToPop=1,l}}},948:(e,t,n)=>{"use strict";var r=n(643);function o(){}function a(){}a.resetWarningCache=o,e.exports=function(){function e(e,t,n,o,a,s){if(s!==r){var i=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:o};return n.PropTypes=n,n}},762:(e,t,n)=>{e.exports=n(948)()},643:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},706:(e,t,n)=>{"use strict";var r=n(442),o=Symbol.for("react.element"),a=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,n){var r,a={},c=null,u=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)s.call(t,r)&&!l.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===a[r]&&(a[r]=t[r]);return{$$typeof:o,type:e,key:c,ref:u,props:a,_owner:i.current}}t.Fragment=a,t.jsx=c,t.jsxs=c},74:(e,t,n)=>{"use strict";e.exports=n(706)},773:e=>{"use strict";e.exports=function(){}},442:t=>{"use strict";t.exports=e},3:e=>{"use strict";e.exports=t},368:(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=s(e,a(n)))}return e}function a(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)r.call(e,n)&&e[n]&&(t=s(t,n));return t}function s(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()}},r={};function o(e){var t=r[e];if(void 0!==t)return t.exports;var a=r[e]={exports:{}};return n[e](a,a.exports,o),a.exports}o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var a={};return(()=>{"use strict";o.r(a),o.d(a,{Accordion:()=>Pe,AccordionBody:()=>ye,AccordionButton:()=>Ne,AccordionCollapse:()=>he,AccordionContext:()=>me,AccordionHeader:()=>je,AccordionItem:()=>Re,Alert:()=>it,AlertHeading:()=>Me,AlertLink:()=>Ze,Anchor:()=>lt,Badge:()=>ut,Breadcrumb:()=>mt,BreadcrumbItem:()=>ft,Button:()=>ht,ButtonGroup:()=>bt,ButtonToolbar:()=>yt,Card:()=>Vt,CardBody:()=>Et,CardFooter:()=>Ct,CardGroup:()=>Ut,CardHeader:()=>kt,CardImg:()=>Tt,CardImgOverlay:()=>St,CardLink:()=>It,CardSubtitle:()=>At,CardText:()=>Ft,CardTitle:()=>Kt,Carousel:()=>on,CarouselCaption:()=>Jt,CarouselItem:()=>en,CloseButton:()=>at,Col:()=>ln,Collapse:()=>de,Container:()=>un,Dropdown:()=>Qo,DropdownButton:()=>oa,DropdownDivider:()=>Do,DropdownHeader:()=>Mo,DropdownItem:()=>Ao,DropdownItemText:()=>Fo,DropdownMenu:()=>Xo,DropdownToggle:()=>Zo,Fade:()=>et,Figure:()=>pa,FigureCaption:()=>da,FigureImage:()=>ca,FloatingLabel:()=>_a,Form:()=>Va,FormCheck:()=>Na,FormControl:()=>ja,FormFloating:()=>Ra,FormGroup:()=>Pa,FormLabel:()=>$a,FormSelect:()=>Ma,FormText:()=>Aa,Image:()=>ia,InputGroup:()=>Ga,ListGroup:()=>us,ListGroupItem:()=>ls,Modal:()=>ei,ModalBody:()=>Bs,ModalDialog:()=>_s,ModalFooter:()=>Ws,ModalHeader:()=>qs,ModalTitle:()=>Ys,Nav:()=>si,NavDropdown:()=>Bi,NavItem:()=>ni,NavLink:()=>oi,Navbar:()=>Li,NavbarBrand:()=>li,NavbarCollapse:()=>ui,NavbarOffcanvas:()=>Si,NavbarText:()=>Ii,NavbarToggle:()=>fi,Offcanvas:()=>Ti,OffcanvasBody:()=>bi,OffcanvasHeader:()=>Ni,OffcanvasTitle:()=>Oi,OffcanvasToggling:()=>wi,Overlay:()=>Qi,OverlayTrigger:()=>tl,PageItem:()=>rl,Pagination:()=>dl,Placeholder:()=>hl,PlaceholderButton:()=>ml,Popover:()=>Xi,PopoverBody:()=>zi,PopoverHeader:()=>Wi,ProgressBar:()=>wl,Ratio:()=>Nl,Row:()=>jl,SSRProvider:()=>$l,Spinner:()=>Rl,SplitButton:()=>Tl,Stack:()=>Il,Tab:()=>Jl,TabContainer:()=>zl,TabContent:()=>ql,TabPane:()=>Xl,Table:()=>ec,Tabs:()=>oc,ThemeProvider:()=>N,Toast:()=>mc,ToastBody:()=>fc,ToastContainer:()=>xc,ToastHeader:()=>uc,ToggleButton:()=>yc,ToggleButtonGroup:()=>Ec,Tooltip:()=>Zi,useAccordionButton:()=>we});var e=o(368),t=o.n(e),n=o(442),r=o.n(n);function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},s.apply(this,arguments)}var i=o(737),l=o.n(i);function c(e){return"default"+e.charAt(0).toUpperCase()+e.substr(1)}function u(e){var t=function(e){if("object"!=typeof e||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}function d(e,t,r){var o=(0,n.useRef)(void 0!==e),a=(0,n.useState)(t),s=a[0],i=a[1],l=void 0!==e,c=o.current;return o.current=l,!l&&c&&s!==t&&i(t),[l?e:s,(0,n.useCallback)((function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];r&&r.apply(void 0,[e].concat(n)),i(e)}),[r])]}function f(e,t){return Object.keys(t).reduce((function(n,r){var o,a=n,i=a[c(r)],l=a[r],f=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(a,[c(r),r].map(u)),p=t[r],m=d(l,i,e[p]),v=m[0],h=m[1];return s({},f,((o={})[r]=v,o[p]=h,o))}),e)}var p=o(74);const m=["xxl","xl","lg","md","sm","xs"],v="xs",h=n.createContext({prefixes:{},breakpoints:m,minBreakpoint:v}),{Consumer:x,Provider:b}=h;function g(e,t){const{prefixes:r}=(0,n.useContext)(h);return e||r[t]||t}function y(){const{breakpoints:e}=(0,n.useContext)(h);return e}function w(){const{minBreakpoint:e}=(0,n.useContext)(h);return e}function E(){const{dir:e}=(0,n.useContext)(h);return"rtl"===e}const N=function({prefixes:e={},breakpoints:t=m,minBreakpoint:r=v,dir:o,children:a}){const s=(0,n.useMemo)((()=>({prefixes:{...e},breakpoints:t,minBreakpoint:r,dir:o})),[e,t,r,o]);return(0,p.jsx)(b,{value:s,children:a})};function C(e){return e&&e.ownerDocument||document}var j=/([A-Z])/g,O=/^ms-/;function R(e){return function(e){return e.replace(j,"-$1").toLowerCase()}(e).replace(O,"-ms-")}var k=/^((translate|rotate|scale)(X|Y|Z|3d)?|matrix(3d)?|perspective|skew(X|Y)?)$/i;const P=function(e,t){var n="",r="";if("string"==typeof t)return e.style.getPropertyValue(R(t))||function(e,t){return function(e){var t=C(e);return t&&t.defaultView||window}(e).getComputedStyle(e,t)}(e).getPropertyValue(R(t));Object.keys(t).forEach((function(o){var a=t[o];a||0===a?function(e){return!(!e||!k.test(e))}(o)?r+=o+"("+a+") ":n+=R(o)+": "+a+";":e.style.removeProperty(R(o))})),r&&(n+="transform: "+r+";"),e.style.cssText+=";"+n};function T(e,t){return T=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},T(e,t)}var $=o(3),S=o.n($);const D=r().createContext(null);var I="unmounted",M="exited",L="entering",A="entered",B="exiting",F=function(e){var t,n;function o(t,n){var r;r=e.call(this,t,n)||this;var o,a=n&&!n.isMounting?t.enter:t.appear;return r.appearStatus=null,t.in?a?(o=M,r.appearStatus=L):o=A:o=t.unmountOnExit||t.mountOnEnter?I:M,r.state={status:o},r.nextCallback=null,r}n=e,(t=o).prototype=Object.create(n.prototype),t.prototype.constructor=t,T(t,n),o.getDerivedStateFromProps=function(e,t){return e.in&&t.status===I?{status:M}:null};var a=o.prototype;return a.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},a.componentDidUpdate=function(e){var t=null;if(e!==this.props){var n=this.state.status;this.props.in?n!==L&&n!==A&&(t=L):n!==L&&n!==A||(t=B)}this.updateStatus(!1,t)},a.componentWillUnmount=function(){this.cancelNextCallback()},a.getTimeouts=function(){var e,t,n,r=this.props.timeout;return e=t=n=r,null!=r&&"number"!=typeof r&&(e=r.exit,t=r.enter,n=void 0!==r.appear?r.appear:t),{exit:e,enter:t,appear:n}},a.updateStatus=function(e,t){if(void 0===e&&(e=!1),null!==t)if(this.cancelNextCallback(),t===L){if(this.props.unmountOnExit||this.props.mountOnEnter){var n=this.props.nodeRef?this.props.nodeRef.current:S().findDOMNode(this);n&&function(e){e.scrollTop}(n)}this.performEnter(e)}else this.performExit();else this.props.unmountOnExit&&this.state.status===M&&this.setState({status:I})},a.performEnter=function(e){var t=this,n=this.props.enter,r=this.context?this.context.isMounting:e,o=this.props.nodeRef?[r]:[S().findDOMNode(this),r],a=o[0],s=o[1],i=this.getTimeouts(),l=r?i.appear:i.enter;e||n?(this.props.onEnter(a,s),this.safeSetState({status:L},(function(){t.props.onEntering(a,s),t.onTransitionEnd(l,(function(){t.safeSetState({status:A},(function(){t.props.onEntered(a,s)}))}))}))):this.safeSetState({status:A},(function(){t.props.onEntered(a)}))},a.performExit=function(){var e=this,t=this.props.exit,n=this.getTimeouts(),r=this.props.nodeRef?void 0:S().findDOMNode(this);t?(this.props.onExit(r),this.safeSetState({status:B},(function(){e.props.onExiting(r),e.onTransitionEnd(n.exit,(function(){e.safeSetState({status:M},(function(){e.props.onExited(r)}))}))}))):this.safeSetState({status:M},(function(){e.props.onExited(r)}))},a.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},a.safeSetState=function(e,t){t=this.setNextCallback(t),this.setState(e,t)},a.setNextCallback=function(e){var t=this,n=!0;return this.nextCallback=function(r){n&&(n=!1,t.nextCallback=null,e(r))},this.nextCallback.cancel=function(){n=!1},this.nextCallback},a.onTransitionEnd=function(e,t){this.setNextCallback(t);var n=this.props.nodeRef?this.props.nodeRef.current:S().findDOMNode(this),r=null==e&&!this.props.addEndListener;if(n&&!r){if(this.props.addEndListener){var o=this.props.nodeRef?[this.nextCallback]:[n,this.nextCallback],a=o[0],s=o[1];this.props.addEndListener(a,s)}null!=e&&setTimeout(this.nextCallback,e)}else setTimeout(this.nextCallback,0)},a.render=function(){var e=this.state.status;if(e===I)return null;var t=this.props,n=t.children,o=(t.in,t.mountOnEnter,t.unmountOnExit,t.appear,t.enter,t.exit,t.timeout,t.addEndListener,t.onEnter,t.onEntering,t.onEntered,t.onExit,t.onExiting,t.onExited,t.nodeRef,function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(t,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]));return r().createElement(D.Provider,{value:null},"function"==typeof n?n(e,o):r().cloneElement(r().Children.only(n),o))},o}(r().Component);function H(){}F.contextType=D,F.propTypes={},F.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:H,onEntering:H,onEntered:H,onExit:H,onExiting:H,onExited:H},F.UNMOUNTED=I,F.EXITED=M,F.ENTERING=L,F.ENTERED=A,F.EXITING=B;const _=F;function K(e){return"Escape"===e.code||27===e.keyCode}function W(e){if(!e||"function"==typeof e)return null;const{major:t}=function(){const e=n.version.split(".");return{major:+e[0],minor:+e[1],patch:+e[2]}}();return t>=19?e.props.ref:e.ref}const V=!("undefined"==typeof window||!window.document||!window.document.createElement);var z=!1,U=!1;try{var q={get passive(){return z=!0},get once(){return U=z=!0}};V&&(window.addEventListener("test",q,q),window.removeEventListener("test",q,!0))}catch(e){}const G=function(e,t,n,r){if(r&&"boolean"!=typeof r&&!U){var o=r.once,a=r.capture,s=n;!U&&o&&(s=n.__once||function e(r){this.removeEventListener(t,e,a),n.call(this,r)},n.__once=s),e.addEventListener(t,s,z?r:a)}e.addEventListener(t,n,r)},X=function(e,t,n,r){var o=r&&"boolean"!=typeof r?r.capture:r;e.removeEventListener(t,n,o),n.__once&&e.removeEventListener(t,n.__once,o)},Y=function(e,t,n,r){return G(e,t,n,r),function(){X(e,t,n,r)}};function Z(e,t,n,r){var o,a;null==n&&(a=-1===(o=P(e,"transitionDuration")||"").indexOf("ms")?1e3:1,n=parseFloat(o)*a||0);var s=function(e,t,n){void 0===n&&(n=5);var r=!1,o=setTimeout((function(){r||function(e,t,n,r){if(void 0===n&&(n=!1),void 0===r&&(r=!0),e){var o=document.createEvent("HTMLEvents");o.initEvent("transitionend",n,r),e.dispatchEvent(o)}}(e,0,!0)}),t+n),a=Y(e,"transitionend",(function(){r=!0}),{once:!0});return function(){clearTimeout(o),a()}}(e,n,r),i=Y(e,"transitionend",t);return function(){s(),i()}}function J(e,t){const n=P(e,t)||"",r=-1===n.indexOf("ms")?1e3:1;return parseFloat(n)*r}function Q(e,t){const n=J(e,"transitionDuration"),r=J(e,"transitionDelay"),o=Z(e,(n=>{n.target===e&&(o(),t(n))}),n+r)}const ee=function(...e){return e.filter((e=>null!=e)).reduce(((e,t)=>{if("function"!=typeof t)throw new Error("Invalid Argument Type, must only provide functions, undefined, or null.");return null===e?t:function(...n){e.apply(this,n),t.apply(this,n)}}),null)};function te(e){e.offsetHeight}const ne=e=>e&&"function"!=typeof e?t=>{e.current=t}:e,re=function(e,t){return(0,n.useMemo)((()=>function(e,t){const n=ne(e),r=ne(t);return e=>{n&&n(e),r&&r(e)}}(e,t)),[e,t])};function oe(e){return e&&"setState"in e?S().findDOMNode(e):null!=e?e:null}const ae=r().forwardRef((({onEnter:e,onEntering:t,onEntered:o,onExit:a,onExiting:s,onExited:i,addEndListener:l,children:c,childRef:u,...d},f)=>{const m=(0,n.useRef)(null),v=re(m,u),h=e=>{v(oe(e))},x=e=>t=>{e&&m.current&&e(m.current,t)},b=(0,n.useCallback)(x(e),[e]),g=(0,n.useCallback)(x(t),[t]),y=(0,n.useCallback)(x(o),[o]),w=(0,n.useCallback)(x(a),[a]),E=(0,n.useCallback)(x(s),[s]),N=(0,n.useCallback)(x(i),[i]),C=(0,n.useCallback)(x(l),[l]);return(0,p.jsx)(_,{ref:f,...d,onEnter:b,onEntered:y,onEntering:g,onExit:w,onExited:N,onExiting:E,addEndListener:C,nodeRef:m,children:"function"==typeof c?(e,t)=>c(e,{...t,ref:h}):r().cloneElement(c,{ref:h})})}));ae.displayName="TransitionWrapper";const se=ae,ie={height:["marginTop","marginBottom"],width:["marginLeft","marginRight"]};function le(e,t){const n=t[`offset${e[0].toUpperCase()}${e.slice(1)}`],r=ie[e];return n+parseInt(P(t,r[0]),10)+parseInt(P(t,r[1]),10)}const ce={[M]:"collapse",[B]:"collapsing",[L]:"collapsing",[A]:"collapse show"},ue=r().forwardRef((({onEnter:e,onEntering:o,onEntered:a,onExit:s,onExiting:i,className:l,children:c,dimension:u="height",in:d=!1,timeout:f=300,mountOnEnter:m=!1,unmountOnExit:v=!1,appear:h=!1,getDimensionValue:x=le,...b},g)=>{const y="function"==typeof u?u():u,w=(0,n.useMemo)((()=>ee((e=>{e.style[y]="0"}),e)),[y,e]),E=(0,n.useMemo)((()=>ee((e=>{const t=`scroll${y[0].toUpperCase()}${y.slice(1)}`;e.style[y]=`${e[t]}px`}),o)),[y,o]),N=(0,n.useMemo)((()=>ee((e=>{e.style[y]=null}),a)),[y,a]),C=(0,n.useMemo)((()=>ee((e=>{e.style[y]=`${x(y,e)}px`,te(e)}),s)),[s,x,y]),j=(0,n.useMemo)((()=>ee((e=>{e.style[y]=null}),i)),[y,i]);return(0,p.jsx)(se,{ref:g,addEndListener:Q,...b,"aria-expanded":b.role?d:null,onEnter:w,onEntering:E,onEntered:N,onExit:C,onExiting:j,childRef:W(c),in:d,timeout:f,mountOnEnter:m,unmountOnExit:v,appear:h,children:(e,n)=>r().cloneElement(c,{...n,className:t()(l,c.props.className,ce[e],"width"===y&&"collapse-horizontal")})})}));ue.displayName="Collapse";const de=ue;function fe(e,t){return Array.isArray(e)?e.includes(t):e===t}const pe=n.createContext({});pe.displayName="AccordionContext";const me=pe,ve=n.forwardRef((({as:e="div",bsPrefix:r,className:o,children:a,eventKey:s,...i},l)=>{const{activeEventKey:c}=(0,n.useContext)(me);return r=g(r,"accordion-collapse"),(0,p.jsx)(de,{ref:l,in:fe(c,s),...i,className:t()(o,r),children:(0,p.jsx)(e,{children:n.Children.only(a)})})}));ve.displayName="AccordionCollapse";const he=ve,xe=n.createContext({eventKey:""});xe.displayName="AccordionItemContext";const be=xe,ge=n.forwardRef((({as:e="div",bsPrefix:r,className:o,onEnter:a,onEntering:s,onEntered:i,onExit:l,onExiting:c,onExited:u,...d},f)=>{r=g(r,"accordion-body");const{eventKey:m}=(0,n.useContext)(be);return(0,p.jsx)(he,{eventKey:m,onEnter:a,onEntering:s,onEntered:i,onExit:l,onExiting:c,onExited:u,children:(0,p.jsx)(e,{ref:f,...d,className:t()(o,r)})})}));ge.displayName="AccordionBody";const ye=ge;function we(e,t){const{activeEventKey:r,onSelect:o,alwaysOpen:a}=(0,n.useContext)(me);return n=>{let s=e===r?null:e;a&&(s=Array.isArray(r)?r.includes(e)?r.filter((t=>t!==e)):[...r,e]:[e]),null==o||o(s,n),null==t||t(n)}}const Ee=n.forwardRef((({as:e="button",bsPrefix:r,className:o,onClick:a,...s},i)=>{r=g(r,"accordion-button");const{eventKey:l}=(0,n.useContext)(be),c=we(l,a),{activeEventKey:u}=(0,n.useContext)(me);return"button"===e&&(s.type="button"),(0,p.jsx)(e,{ref:i,onClick:c,...s,"aria-expanded":Array.isArray(u)?u.includes(l):l===u,className:t()(o,r,!fe(u,l)&&"collapsed")})}));Ee.displayName="AccordionButton";const Ne=Ee,Ce=n.forwardRef((({as:e="h2","aria-controls":n,bsPrefix:r,className:o,children:a,onClick:s,...i},l)=>(r=g(r,"accordion-header"),(0,p.jsx)(e,{ref:l,...i,className:t()(o,r),children:(0,p.jsx)(Ne,{onClick:s,"aria-controls":n,children:a})}))));Ce.displayName="AccordionHeader";const je=Ce,Oe=n.forwardRef((({as:e="div",bsPrefix:r,className:o,eventKey:a,...s},i)=>{r=g(r,"accordion-item");const l=(0,n.useMemo)((()=>({eventKey:a})),[a]);return(0,p.jsx)(be.Provider,{value:l,children:(0,p.jsx)(e,{ref:i,...s,className:t()(o,r)})})}));Oe.displayName="AccordionItem";const Re=Oe,ke=n.forwardRef(((e,r)=>{const{as:o="div",activeKey:a,bsPrefix:s,className:i,onSelect:l,flush:c,alwaysOpen:u,...d}=f(e,{activeKey:"onSelect"}),m=g(s,"accordion"),v=(0,n.useMemo)((()=>({activeEventKey:a,onSelect:l,alwaysOpen:u})),[a,l,u]);return(0,p.jsx)(me.Provider,{value:v,children:(0,p.jsx)(o,{ref:r,...d,className:t()(i,m,c&&`${m}-flush`)})})}));ke.displayName="Accordion";const Pe=Object.assign(ke,{Button:Ne,Collapse:he,Item:Re,Header:je,Body:ye}),Te=function(e){const t=(0,n.useRef)(e);return(0,n.useEffect)((()=>{t.current=e}),[e]),t};function $e(e){const t=Te(e);return(0,n.useCallback)((function(...e){return t.current&&t.current(...e)}),[t])}const Se=e=>n.forwardRef(((n,r)=>(0,p.jsx)("div",{...n,ref:r,className:t()(n.className,e)}))),De=Se("h4");De.displayName="DivStyledAsH4";const Ie=n.forwardRef((({className:e,bsPrefix:n,as:r=De,...o},a)=>(n=g(n,"alert-heading"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));Ie.displayName="AlertHeading";const Me=Ie;function Le(){return(0,n.useState)(null)}const Ae=function(e){const t=(0,n.useRef)(e);return(0,n.useEffect)((()=>{t.current=e}),[e]),t};function Be(e){const t=Ae(e);return(0,n.useCallback)((function(...e){return t.current&&t.current(...e)}),[t])}function Fe(){const e=(0,n.useRef)(!0),t=(0,n.useRef)((()=>e.current));return(0,n.useEffect)((()=>(e.current=!0,()=>{e.current=!1})),[]),t.current}function He(e){const t=(0,n.useRef)(null);return(0,n.useEffect)((()=>{t.current=e})),t.current}const _e=void 0!==o.g&&o.g.navigator&&"ReactNative"===o.g.navigator.product,Ke="undefined"!=typeof document||_e?n.useLayoutEffect:n.useEffect;new WeakMap;const We=["as","disabled"];function Ve({tagName:e,disabled:t,href:n,target:r,rel:o,role:a,onClick:s,tabIndex:i=0,type:l}){e||(e=null!=n||null!=r||null!=o?"a":"button");const c={tagName:e};if("button"===e)return[{type:l||"button",disabled:t},c];const u=r=>{(t||"a"===e&&function(e){return!e||"#"===e.trim()}(n))&&r.preventDefault(),t?r.stopPropagation():null==s||s(r)};return"a"===e&&(n||(n="#"),t&&(n=void 0)),[{role:null!=a?a:"button",disabled:void 0,tabIndex:t?void 0:i,href:n,target:"a"===e?r:void 0,"aria-disabled":t||void 0,rel:"a"===e?o:void 0,onClick:u,onKeyDown:e=>{" "===e.key&&(e.preventDefault(),u(e))}},c]}const ze=n.forwardRef(((e,t)=>{let{as:n,disabled:r}=e,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,We);const[a,{tagName:s}]=Ve(Object.assign({tagName:n,disabled:r},o));return(0,p.jsx)(s,Object.assign({},o,a,{ref:t}))}));ze.displayName="Button";const Ue=ze,qe=["onKeyDown"],Ge=n.forwardRef(((e,t)=>{let{onKeyDown:n}=e,r=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,qe);const[o]=Ve(Object.assign({tagName:"a"},r)),a=Be((e=>{o.onKeyDown(e),null==n||n(e)}));return(s=r.href)&&"#"!==s.trim()&&"button"!==r.role?(0,p.jsx)("a",Object.assign({ref:t},r,{onKeyDown:n})):(0,p.jsx)("a",Object.assign({ref:t},r,o,{onKeyDown:a}));var s}));Ge.displayName="Anchor";const Xe=Ge,Ye=n.forwardRef((({className:e,bsPrefix:n,as:r=Xe,...o},a)=>(n=g(n,"alert-link"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));Ye.displayName="AlertLink";const Ze=Ye,Je={[L]:"show",[A]:"show"},Qe=n.forwardRef((({className:e,children:r,transitionClasses:o={},onEnter:a,...s},i)=>{const l={in:!1,timeout:300,mountOnEnter:!1,unmountOnExit:!1,appear:!1,...s},c=(0,n.useCallback)(((e,t)=>{te(e),null==a||a(e,t)}),[a]);return(0,p.jsx)(se,{ref:i,addEndListener:Q,...l,onEnter:c,childRef:W(r),children:(a,s)=>n.cloneElement(r,{...s,className:t()("fade",e,r.props.className,Je[a],o[a])})})}));Qe.displayName="Fade";const et=Qe;var tt=o(762),nt=o.n(tt);const rt={"aria-label":nt().string,onClick:nt().func,variant:nt().oneOf(["white"])},ot=n.forwardRef((({className:e,variant:n,"aria-label":r="Close",...o},a)=>(0,p.jsx)("button",{ref:a,type:"button",className:t()("btn-close",n&&`btn-close-${n}`,e),"aria-label":r,...o})));ot.displayName="CloseButton",ot.propTypes=rt;const at=ot,st=n.forwardRef(((e,n)=>{const{bsPrefix:r,show:o=!0,closeLabel:a="Close alert",closeVariant:s,className:i,children:l,variant:c="primary",onClose:u,dismissible:d,transition:m=et,...v}=f(e,{show:"onClose"}),h=g(r,"alert"),x=$e((e=>{u&&u(!1,e)})),b=!0===m?et:m,y=(0,p.jsxs)("div",{role:"alert",...b?void 0:v,ref:n,className:t()(i,h,c&&`${h}-${c}`,d&&`${h}-dismissible`),children:[d&&(0,p.jsx)(at,{onClick:x,"aria-label":a,variant:s}),l]});return b?(0,p.jsx)(b,{unmountOnExit:!0,...v,ref:void 0,in:o,children:y}):o?y:null}));st.displayName="Alert";const it=Object.assign(st,{Link:Ze,Heading:Me}),lt=Xe,ct=n.forwardRef((({bsPrefix:e,bg:n="primary",pill:r=!1,text:o,className:a,as:s="span",...i},l)=>{const c=g(e,"badge");return(0,p.jsx)(s,{ref:l,...i,className:t()(a,c,r&&"rounded-pill",o&&`text-${o}`,n&&`bg-${n}`)})}));ct.displayName="Badge";const ut=ct,dt=n.forwardRef((({bsPrefix:e,active:n=!1,children:r,className:o,as:a="li",linkAs:s=Xe,linkProps:i={},href:l,title:c,target:u,...d},f)=>{const m=g(e,"breadcrumb-item");return(0,p.jsx)(a,{ref:f,...d,className:t()(m,o,{active:n}),"aria-current":n?"page":void 0,children:n?r:(0,p.jsx)(s,{...i,href:l,title:c,target:u,children:r})})}));dt.displayName="BreadcrumbItem";const ft=dt,pt=n.forwardRef((({bsPrefix:e,className:n,listProps:r={},children:o,label:a="breadcrumb",as:s="nav",...i},l)=>{const c=g(e,"breadcrumb");return(0,p.jsx)(s,{"aria-label":a,className:n,ref:l,...i,children:(0,p.jsx)("ol",{...r,className:t()(c,null==r?void 0:r.className),children:o})})}));pt.displayName="Breadcrumb";const mt=Object.assign(pt,{Item:ft}),vt=n.forwardRef((({as:e,bsPrefix:n,variant:r="primary",size:o,active:a=!1,disabled:s=!1,className:i,...l},c)=>{const u=g(n,"btn"),[d,{tagName:f}]=Ve({tagName:e,disabled:s,...l}),m=f;return(0,p.jsx)(m,{...d,...l,ref:c,disabled:s,className:t()(i,u,a&&"active",r&&`${u}-${r}`,o&&`${u}-${o}`,l.href&&s&&"disabled")})}));vt.displayName="Button";const ht=vt,xt=n.forwardRef((({bsPrefix:e,size:n,vertical:r=!1,className:o,role:a="group",as:s="div",...i},l)=>{const c=g(e,"btn-group");let u=c;return r&&(u=`${c}-vertical`),(0,p.jsx)(s,{...i,ref:l,role:a,className:t()(o,u,n&&`${c}-${n}`)})}));xt.displayName="ButtonGroup";const bt=xt,gt=n.forwardRef((({bsPrefix:e,className:n,role:r="toolbar",...o},a)=>{const s=g(e,"btn-toolbar");return(0,p.jsx)("div",{...o,ref:a,className:t()(n,s),role:r})}));gt.displayName="ButtonToolbar";const yt=gt,wt=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=g(n,"card-body"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));wt.displayName="CardBody";const Et=wt,Nt=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=g(n,"card-footer"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));Nt.displayName="CardFooter";const Ct=Nt,jt=n.createContext(null);jt.displayName="CardHeaderContext";const Ot=jt,Rt=n.forwardRef((({bsPrefix:e,className:r,as:o="div",...a},s)=>{const i=g(e,"card-header"),l=(0,n.useMemo)((()=>({cardHeaderBsPrefix:i})),[i]);return(0,p.jsx)(Ot.Provider,{value:l,children:(0,p.jsx)(o,{ref:s,...a,className:t()(r,i)})})}));Rt.displayName="CardHeader";const kt=Rt,Pt=n.forwardRef((({bsPrefix:e,className:n,variant:r,as:o="img",...a},s)=>{const i=g(e,"card-img");return(0,p.jsx)(o,{ref:s,className:t()(r?`${i}-${r}`:i,n),...a})}));Pt.displayName="CardImg";const Tt=Pt,$t=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=g(n,"card-img-overlay"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));$t.displayName="CardImgOverlay";const St=$t,Dt=n.forwardRef((({className:e,bsPrefix:n,as:r="a",...o},a)=>(n=g(n,"card-link"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));Dt.displayName="CardLink";const It=Dt,Mt=Se("h6"),Lt=n.forwardRef((({className:e,bsPrefix:n,as:r=Mt,...o},a)=>(n=g(n,"card-subtitle"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));Lt.displayName="CardSubtitle";const At=Lt,Bt=n.forwardRef((({className:e,bsPrefix:n,as:r="p",...o},a)=>(n=g(n,"card-text"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));Bt.displayName="CardText";const Ft=Bt,Ht=Se("h5"),_t=n.forwardRef((({className:e,bsPrefix:n,as:r=Ht,...o},a)=>(n=g(n,"card-title"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));_t.displayName="CardTitle";const Kt=_t,Wt=n.forwardRef((({bsPrefix:e,className:n,bg:r,text:o,border:a,body:s=!1,children:i,as:l="div",...c},u)=>{const d=g(e,"card");return(0,p.jsx)(l,{ref:u,...c,className:t()(n,d,r&&`bg-${r}`,o&&`text-${o}`,a&&`border-${a}`),children:s?(0,p.jsx)(Et,{children:i}):i})}));Wt.displayName="Card";const Vt=Object.assign(Wt,{Img:Tt,Title:Kt,Subtitle:At,Body:Et,Link:It,Text:Ft,Header:kt,Footer:Ct,ImgOverlay:St}),zt=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=g(n,"card-group"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));zt.displayName="CardGroup";const Ut=zt;function qt(e){const t=function(e){const t=(0,n.useRef)(e);return t.current=e,t}(e);(0,n.useEffect)((()=>()=>t.current()),[])}const Gt=2**31-1;function Xt(e,t,n){const r=n-Date.now();e.current=r<=Gt?setTimeout(t,r):setTimeout((()=>Xt(e,t,n)),Gt)}function Yt(){const e=function(){const e=(0,n.useRef)(!0),t=(0,n.useRef)((()=>e.current));return(0,n.useEffect)((()=>(e.current=!0,()=>{e.current=!1})),[]),t.current}(),t=(0,n.useRef)();return qt((()=>clearTimeout(t.current))),(0,n.useMemo)((()=>{const n=()=>clearTimeout(t.current);return{set:function(r,o=0){e()&&(n(),o<=Gt?t.current=setTimeout(r,o):Xt(t,r,Date.now()+o))},clear:n,handleRef:t}}),[])}const Zt=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=g(n,"carousel-caption"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));Zt.displayName="CarouselCaption";const Jt=Zt,Qt=n.forwardRef((({as:e="div",bsPrefix:n,className:r,...o},a)=>{const s=t()(r,g(n,"carousel-item"));return(0,p.jsx)(e,{ref:a,...o,className:s})}));Qt.displayName="CarouselItem";const en=Qt;function tn(e,t){let r=0;return n.Children.map(e,(e=>n.isValidElement(e)?t(e,r++):e))}function nn(e,t){let r=0;n.Children.forEach(e,(e=>{n.isValidElement(e)&&t(e,r++)}))}const rn=n.forwardRef((({defaultActiveIndex:e=0,...r},o)=>{const{as:a="div",bsPrefix:s,slide:i=!0,fade:l=!1,controls:c=!0,indicators:u=!0,indicatorLabels:d=[],activeIndex:m,onSelect:v,onSlide:h,onSlid:x,interval:b=5e3,keyboard:y=!0,onKeyDown:w,pause:N="hover",onMouseOver:C,onMouseOut:j,wrap:O=!0,touch:R=!0,onTouchStart:k,onTouchMove:P,onTouchEnd:T,prevIcon:$=(0,p.jsx)("span",{"aria-hidden":"true",className:"carousel-control-prev-icon"}),prevLabel:S="Previous",nextIcon:D=(0,p.jsx)("span",{"aria-hidden":"true",className:"carousel-control-next-icon"}),nextLabel:I="Next",variant:M,className:L,children:A,...B}=f({defaultActiveIndex:e,...r},{activeIndex:"onSelect"}),F=g(s,"carousel"),H=E(),_=(0,n.useRef)(null),[K,W]=(0,n.useState)("next"),[V,z]=(0,n.useState)(!1),[U,q]=(0,n.useState)(!1),[G,X]=(0,n.useState)(m||0);(0,n.useEffect)((()=>{U||m===G||(_.current?W(_.current):W((m||0)>G?"next":"prev"),i&&q(!0),X(m||0))}),[m,U,G,i]),(0,n.useEffect)((()=>{_.current&&(_.current=null)}));let Y,Z=0;nn(A,((e,t)=>{++Z,t===m&&(Y=e.props.interval)}));const J=Te(Y),ee=(0,n.useCallback)((e=>{if(U)return;let t=G-1;if(t<0){if(!O)return;t=Z-1}_.current="prev",null==v||v(t,e)}),[U,G,v,O,Z]),ne=$e((e=>{if(U)return;let t=G+1;if(t>=Z){if(!O)return;t=0}_.current="next",null==v||v(t,e)})),re=(0,n.useRef)();(0,n.useImperativeHandle)(o,(()=>({element:re.current,prev:ee,next:ne})));const oe=$e((()=>{!document.hidden&&function(e){if(!(e&&e.style&&e.parentNode&&e.parentNode.style))return!1;const t=getComputedStyle(e);return"none"!==t.display&&"hidden"!==t.visibility&&"none"!==getComputedStyle(e.parentNode).display}(re.current)&&(H?ee():ne())})),ae="next"===K?"start":"end";!function(e,t){const r=(0,n.useRef)(!0);(0,n.useEffect)((()=>{if(!r.current)return e();r.current=!1}),t)}((()=>{i||(null==h||h(G,ae),null==x||x(G,ae))}),[G]);const ie=`${F}-item-${K}`,le=`${F}-item-${ae}`,ce=(0,n.useCallback)((e=>{te(e),null==h||h(G,ae)}),[h,G,ae]),ue=(0,n.useCallback)((()=>{q(!1),null==x||x(G,ae)}),[x,G,ae]),de=(0,n.useCallback)((e=>{if(y&&!/input|textarea/i.test(e.target.tagName))switch(e.key){case"ArrowLeft":return e.preventDefault(),void(H?ne(e):ee(e));case"ArrowRight":return e.preventDefault(),void(H?ee(e):ne(e))}null==w||w(e)}),[y,w,ee,ne,H]),fe=(0,n.useCallback)((e=>{"hover"===N&&z(!0),null==C||C(e)}),[N,C]),pe=(0,n.useCallback)((e=>{z(!1),null==j||j(e)}),[j]),me=(0,n.useRef)(0),ve=(0,n.useRef)(0),he=Yt(),xe=(0,n.useCallback)((e=>{me.current=e.touches[0].clientX,ve.current=0,"hover"===N&&z(!0),null==k||k(e)}),[N,k]),be=(0,n.useCallback)((e=>{e.touches&&e.touches.length>1?ve.current=0:ve.current=e.touches[0].clientX-me.current,null==P||P(e)}),[P]),ge=(0,n.useCallback)((e=>{if(R){const t=ve.current;Math.abs(t)>40&&(t>0?ee(e):ne(e))}"hover"===N&&he.set((()=>{z(!1)}),b||void 0),null==T||T(e)}),[R,N,ee,ne,he,b,T]),ye=null!=b&&!V&&!U,we=(0,n.useRef)();(0,n.useEffect)((()=>{var e,t;if(!ye)return;const n=H?ee:ne;return we.current=window.setInterval(document.visibilityState?oe:n,null!=(e=null!=(t=J.current)?t:b)?e:void 0),()=>{null!==we.current&&clearInterval(we.current)}}),[ye,ee,ne,J,b,oe,H]);const Ee=(0,n.useMemo)((()=>u&&Array.from({length:Z},((e,t)=>e=>{null==v||v(t,e)}))),[u,Z,v]);return(0,p.jsxs)(a,{ref:re,...B,onKeyDown:de,onMouseOver:fe,onMouseOut:pe,onTouchStart:xe,onTouchMove:be,onTouchEnd:ge,className:t()(L,F,i&&"slide",l&&`${F}-fade`,M&&`${F}-${M}`),children:[u&&(0,p.jsx)("div",{className:`${F}-indicators`,children:tn(A,((e,t)=>(0,p.jsx)("button",{type:"button","data-bs-target":"","aria-label":null!=d&&d.length?d[t]:`Slide ${t+1}`,className:t===G?"active":void 0,onClick:Ee?Ee[t]:void 0,"aria-current":t===G},t)))}),(0,p.jsx)("div",{className:`${F}-inner`,children:tn(A,((e,r)=>{const o=r===G;return i?(0,p.jsx)(se,{in:o,onEnter:o?ce:void 0,onEntered:o?ue:void 0,addEndListener:Q,children:(r,a)=>n.cloneElement(e,{...a,className:t()(e.props.className,o&&"entered"!==r&&ie,("entered"===r||"exiting"===r)&&"active",("entering"===r||"exiting"===r)&&le)})}):n.cloneElement(e,{className:t()(e.props.className,o&&"active")})}))}),c&&(0,p.jsxs)(p.Fragment,{children:[(O||0!==m)&&(0,p.jsxs)(Xe,{className:`${F}-control-prev`,onClick:ee,children:[$,S&&(0,p.jsx)("span",{className:"visually-hidden",children:S})]}),(O||m!==Z-1)&&(0,p.jsxs)(Xe,{className:`${F}-control-next`,onClick:ne,children:[D,I&&(0,p.jsx)("span",{className:"visually-hidden",children:I})]})]})]})}));rn.displayName="Carousel";const on=Object.assign(rn,{Caption:Jt,Item:en});function an({as:e,bsPrefix:n,className:r,...o}){n=g(n,"col");const a=y(),s=w(),i=[],l=[];return a.forEach((e=>{const t=o[e];let r,a,c;delete o[e],"object"==typeof t&&null!=t?({span:r,offset:a,order:c}=t):r=t;const u=e!==s?`-${e}`:"";r&&i.push(!0===r?`${n}${u}`:`${n}${u}-${r}`),null!=c&&l.push(`order${u}-${c}`),null!=a&&l.push(`offset${u}-${a}`)})),[{...o,className:t()(r,...i,...l)},{as:e,bsPrefix:n,spans:i}]}const sn=n.forwardRef(((e,n)=>{const[{className:r,...o},{as:a="div",bsPrefix:s,spans:i}]=an(e);return(0,p.jsx)(a,{...o,ref:n,className:t()(r,!i.length&&s)})}));sn.displayName="Col";const ln=sn,cn=n.forwardRef((({bsPrefix:e,fluid:n=!1,as:r="div",className:o,...a},s)=>{const i=g(e,"container"),l="string"==typeof n?`-${n}`:"-fluid";return(0,p.jsx)(r,{ref:s,...a,className:t()(o,n?`${i}${l}`:i)})}));cn.displayName="Container";const un=cn;var dn=Function.prototype.bind.call(Function.prototype.call,[].slice);function fn(e,t){return dn(e.querySelectorAll(t))}function pn(e,t,r){const o=(0,n.useRef)(void 0!==e),[a,s]=(0,n.useState)(t),i=void 0!==e,l=o.current;return o.current=i,!i&&l&&a!==t&&s(t),[i?e:a,(0,n.useCallback)(((...e)=>{const[t,...n]=e;let o=null==r?void 0:r(t,...n);return s(t),o}),[r])]}function mn(){const[,e]=(0,n.useReducer)((e=>e+1),0);return e}const vn=n.createContext(null);var hn=Object.prototype.hasOwnProperty;function xn(e,t,n){for(n of e.keys())if(bn(n,t))return n}function bn(e,t){var n,r,o;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((r=e.length)===t.length)for(;r--&&bn(e[r],t[r]););return-1===r}if(n===Set){if(e.size!==t.size)return!1;for(r of e){if((o=r)&&"object"==typeof o&&!(o=xn(t,o)))return!1;if(!t.has(o))return!1}return!0}if(n===Map){if(e.size!==t.size)return!1;for(r of e){if((o=r[0])&&"object"==typeof o&&!(o=xn(t,o)))return!1;if(!bn(r[1],t.get(o)))return!1}return!0}if(n===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(n===DataView){if((r=e.byteLength)===t.byteLength)for(;r--&&e.getInt8(r)===t.getInt8(r););return-1===r}if(ArrayBuffer.isView(e)){if((r=e.byteLength)===t.byteLength)for(;r--&&e[r]===t[r];);return-1===r}if(!n||"object"==typeof e){for(n in r=0,e){if(hn.call(e,n)&&++r&&!hn.call(t,n))return!1;if(!(n in t)||!bn(e[n],t[n]))return!1}return Object.keys(t).length===r}}return e!=e&&t!=t}function gn(e){return e.split("-")[0]}function yn(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function wn(e){return e instanceof yn(e).Element||e instanceof Element}function En(e){return e instanceof yn(e).HTMLElement||e instanceof HTMLElement}function Nn(e){return"undefined"!=typeof ShadowRoot&&(e instanceof yn(e).ShadowRoot||e instanceof ShadowRoot)}var Cn=Math.max,jn=Math.min,On=Math.round;function Rn(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function kn(){return!/^((?!chrome|android).)*safari/i.test(Rn())}function Pn(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),o=1,a=1;t&&En(e)&&(o=e.offsetWidth>0&&On(r.width)/e.offsetWidth||1,a=e.offsetHeight>0&&On(r.height)/e.offsetHeight||1);var s=(wn(e)?yn(e):window).visualViewport,i=!kn()&&n,l=(r.left+(i&&s?s.offsetLeft:0))/o,c=(r.top+(i&&s?s.offsetTop:0))/a,u=r.width/o,d=r.height/a;return{width:u,height:d,top:c,right:l+u,bottom:c+d,left:l,x:l,y:c}}function Tn(e){var t=Pn(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function $n(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Nn(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function Sn(e){return e?(e.nodeName||"").toLowerCase():null}function Dn(e){return yn(e).getComputedStyle(e)}function In(e){return["table","td","th"].indexOf(Sn(e))>=0}function Mn(e){return((wn(e)?e.ownerDocument:e.document)||window.document).documentElement}function Ln(e){return"html"===Sn(e)?e:e.assignedSlot||e.parentNode||(Nn(e)?e.host:null)||Mn(e)}function An(e){return En(e)&&"fixed"!==Dn(e).position?e.offsetParent:null}function Bn(e){for(var t=yn(e),n=An(e);n&&In(n)&&"static"===Dn(n).position;)n=An(n);return n&&("html"===Sn(n)||"body"===Sn(n)&&"static"===Dn(n).position)?t:n||function(e){var t=/firefox/i.test(Rn());if(/Trident/i.test(Rn())&&En(e)&&"fixed"===Dn(e).position)return null;var n=Ln(e);for(Nn(n)&&(n=n.host);En(n)&&["html","body"].indexOf(Sn(n))<0;){var r=Dn(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}function Fn(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Hn(e,t,n){return Cn(e,jn(t,n))}function _n(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Kn(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}var Wn="top",Vn="bottom",zn="right",Un="left",qn="auto",Gn=[Wn,Vn,zn,Un],Xn="start",Yn="end",Zn="viewport",Jn="popper",Qn=Gn.reduce((function(e,t){return e.concat([t+"-"+Xn,t+"-"+Yn])}),[]),er=[].concat(Gn,[qn]).reduce((function(e,t){return e.concat([t,t+"-"+Xn,t+"-"+Yn])}),[]),tr=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];const nr={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,o=e.options,a=n.elements.arrow,s=n.modifiersData.popperOffsets,i=gn(n.placement),l=Fn(i),c=[Un,zn].indexOf(i)>=0?"height":"width";if(a&&s){var u=function(e,t){return _n("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:Kn(e,Gn))}(o.padding,n),d=Tn(a),f="y"===l?Wn:Un,p="y"===l?Vn:zn,m=n.rects.reference[c]+n.rects.reference[l]-s[l]-n.rects.popper[c],v=s[l]-n.rects.reference[l],h=Bn(a),x=h?"y"===l?h.clientHeight||0:h.clientWidth||0:0,b=m/2-v/2,g=u[f],y=x-d[c]-u[p],w=x/2-d[c]/2+b,E=Hn(g,w,y),N=l;n.modifiersData[r]=((t={})[N]=E,t.centerOffset=E-w,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&$n(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function rr(e){return e.split("-")[1]}var or={top:"auto",right:"auto",bottom:"auto",left:"auto"};function ar(e){var t,n=e.popper,r=e.popperRect,o=e.placement,a=e.variation,s=e.offsets,i=e.position,l=e.gpuAcceleration,c=e.adaptive,u=e.roundOffsets,d=e.isFixed,f=s.x,p=void 0===f?0:f,m=s.y,v=void 0===m?0:m,h="function"==typeof u?u({x:p,y:v}):{x:p,y:v};p=h.x,v=h.y;var x=s.hasOwnProperty("x"),b=s.hasOwnProperty("y"),g=Un,y=Wn,w=window;if(c){var E=Bn(n),N="clientHeight",C="clientWidth";E===yn(n)&&"static"!==Dn(E=Mn(n)).position&&"absolute"===i&&(N="scrollHeight",C="scrollWidth"),(o===Wn||(o===Un||o===zn)&&a===Yn)&&(y=Vn,v-=(d&&E===w&&w.visualViewport?w.visualViewport.height:E[N])-r.height,v*=l?1:-1),o!==Un&&(o!==Wn&&o!==Vn||a!==Yn)||(g=zn,p-=(d&&E===w&&w.visualViewport?w.visualViewport.width:E[C])-r.width,p*=l?1:-1)}var j,O=Object.assign({position:i},c&&or),R=!0===u?function(e,t){var n=e.x,r=e.y,o=t.devicePixelRatio||1;return{x:On(n*o)/o||0,y:On(r*o)/o||0}}({x:p,y:v},yn(n)):{x:p,y:v};return p=R.x,v=R.y,l?Object.assign({},O,((j={})[y]=b?"0":"",j[g]=x?"0":"",j.transform=(w.devicePixelRatio||1)<=1?"translate("+p+"px, "+v+"px)":"translate3d("+p+"px, "+v+"px, 0)",j)):Object.assign({},O,((t={})[y]=b?v+"px":"",t[g]=x?p+"px":"",t.transform="",t))}const sr={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=void 0===r||r,a=n.adaptive,s=void 0===a||a,i=n.roundOffsets,l=void 0===i||i,c={placement:gn(t.placement),variation:rr(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:o,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,ar(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:s,roundOffsets:l})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,ar(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}};var ir={passive:!0};const lr={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,o=r.scroll,a=void 0===o||o,s=r.resize,i=void 0===s||s,l=yn(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&c.forEach((function(e){e.addEventListener("scroll",n.update,ir)})),i&&l.addEventListener("resize",n.update,ir),function(){a&&c.forEach((function(e){e.removeEventListener("scroll",n.update,ir)})),i&&l.removeEventListener("resize",n.update,ir)}},data:{}};var cr={left:"right",right:"left",bottom:"top",top:"bottom"};function ur(e){return e.replace(/left|right|bottom|top/g,(function(e){return cr[e]}))}var dr={start:"end",end:"start"};function fr(e){return e.replace(/start|end/g,(function(e){return dr[e]}))}function pr(e){var t=yn(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function mr(e){return Pn(Mn(e)).left+pr(e).scrollLeft}function vr(e){var t=Dn(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function hr(e){return["html","body","#document"].indexOf(Sn(e))>=0?e.ownerDocument.body:En(e)&&vr(e)?e:hr(Ln(e))}function xr(e,t){var n;void 0===t&&(t=[]);var r=hr(e),o=r===(null==(n=e.ownerDocument)?void 0:n.body),a=yn(r),s=o?[a].concat(a.visualViewport||[],vr(r)?r:[]):r,i=t.concat(s);return o?i:i.concat(xr(Ln(s)))}function br(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function gr(e,t,n){return t===Zn?br(function(e,t){var n=yn(e),r=Mn(e),o=n.visualViewport,a=r.clientWidth,s=r.clientHeight,i=0,l=0;if(o){a=o.width,s=o.height;var c=kn();(c||!c&&"fixed"===t)&&(i=o.offsetLeft,l=o.offsetTop)}return{width:a,height:s,x:i+mr(e),y:l}}(e,n)):wn(t)?function(e,t){var n=Pn(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(t,n):br(function(e){var t,n=Mn(e),r=pr(e),o=null==(t=e.ownerDocument)?void 0:t.body,a=Cn(n.scrollWidth,n.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),s=Cn(n.scrollHeight,n.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),i=-r.scrollLeft+mr(e),l=-r.scrollTop;return"rtl"===Dn(o||n).direction&&(i+=Cn(n.clientWidth,o?o.clientWidth:0)-a),{width:a,height:s,x:i,y:l}}(Mn(e)))}function yr(e){var t,n=e.reference,r=e.element,o=e.placement,a=o?gn(o):null,s=o?rr(o):null,i=n.x+n.width/2-r.width/2,l=n.y+n.height/2-r.height/2;switch(a){case Wn:t={x:i,y:n.y-r.height};break;case Vn:t={x:i,y:n.y+n.height};break;case zn:t={x:n.x+n.width,y:l};break;case Un:t={x:n.x-r.width,y:l};break;default:t={x:n.x,y:n.y}}var c=a?Fn(a):null;if(null!=c){var u="y"===c?"height":"width";switch(s){case Xn:t[c]=t[c]-(n[u]/2-r[u]/2);break;case Yn:t[c]=t[c]+(n[u]/2-r[u]/2)}}return t}function wr(e,t){void 0===t&&(t={});var n=t,r=n.placement,o=void 0===r?e.placement:r,a=n.strategy,s=void 0===a?e.strategy:a,i=n.boundary,l=void 0===i?"clippingParents":i,c=n.rootBoundary,u=void 0===c?Zn:c,d=n.elementContext,f=void 0===d?Jn:d,p=n.altBoundary,m=void 0!==p&&p,v=n.padding,h=void 0===v?0:v,x=_n("number"!=typeof h?h:Kn(h,Gn)),b=f===Jn?"reference":Jn,g=e.rects.popper,y=e.elements[m?b:f],w=function(e,t,n,r){var o="clippingParents"===t?function(e){var t=xr(Ln(e)),n=["absolute","fixed"].indexOf(Dn(e).position)>=0&&En(e)?Bn(e):e;return wn(n)?t.filter((function(e){return wn(e)&&$n(e,n)&&"body"!==Sn(e)})):[]}(e):[].concat(t),a=[].concat(o,[n]),s=a[0],i=a.reduce((function(t,n){var o=gr(e,n,r);return t.top=Cn(o.top,t.top),t.right=jn(o.right,t.right),t.bottom=jn(o.bottom,t.bottom),t.left=Cn(o.left,t.left),t}),gr(e,s,r));return i.width=i.right-i.left,i.height=i.bottom-i.top,i.x=i.left,i.y=i.top,i}(wn(y)?y:y.contextElement||Mn(e.elements.popper),l,u,s),E=Pn(e.elements.reference),N=yr({reference:E,element:g,strategy:"absolute",placement:o}),C=br(Object.assign({},g,N)),j=f===Jn?C:E,O={top:w.top-j.top+x.top,bottom:j.bottom-w.bottom+x.bottom,left:w.left-j.left+x.left,right:j.right-w.right+x.right},R=e.modifiersData.offset;if(f===Jn&&R){var k=R[o];Object.keys(O).forEach((function(e){var t=[zn,Vn].indexOf(e)>=0?1:-1,n=[Wn,Vn].indexOf(e)>=0?"y":"x";O[e]+=k[n]*t}))}return O}const Er={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var o=n.mainAxis,a=void 0===o||o,s=n.altAxis,i=void 0===s||s,l=n.fallbackPlacements,c=n.padding,u=n.boundary,d=n.rootBoundary,f=n.altBoundary,p=n.flipVariations,m=void 0===p||p,v=n.allowedAutoPlacements,h=t.options.placement,x=gn(h),b=l||(x!==h&&m?function(e){if(gn(e)===qn)return[];var t=ur(e);return[fr(e),t,fr(t)]}(h):[ur(h)]),g=[h].concat(b).reduce((function(e,n){return e.concat(gn(n)===qn?function(e,t){void 0===t&&(t={});var n=t,r=n.placement,o=n.boundary,a=n.rootBoundary,s=n.padding,i=n.flipVariations,l=n.allowedAutoPlacements,c=void 0===l?er:l,u=rr(r),d=u?i?Qn:Qn.filter((function(e){return rr(e)===u})):Gn,f=d.filter((function(e){return c.indexOf(e)>=0}));0===f.length&&(f=d);var p=f.reduce((function(t,n){return t[n]=wr(e,{placement:n,boundary:o,rootBoundary:a,padding:s})[gn(n)],t}),{});return Object.keys(p).sort((function(e,t){return p[e]-p[t]}))}(t,{placement:n,boundary:u,rootBoundary:d,padding:c,flipVariations:m,allowedAutoPlacements:v}):n)}),[]),y=t.rects.reference,w=t.rects.popper,E=new Map,N=!0,C=g[0],j=0;j<g.length;j++){var O=g[j],R=gn(O),k=rr(O)===Xn,P=[Wn,Vn].indexOf(R)>=0,T=P?"width":"height",$=wr(t,{placement:O,boundary:u,rootBoundary:d,altBoundary:f,padding:c}),S=P?k?zn:Un:k?Vn:Wn;y[T]>w[T]&&(S=ur(S));var D=ur(S),I=[];if(a&&I.push($[R]<=0),i&&I.push($[S]<=0,$[D]<=0),I.every((function(e){return e}))){C=O,N=!1;break}E.set(O,I)}if(N)for(var M=function(e){var t=g.find((function(t){var n=E.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return C=t,"break"},L=m?3:1;L>0&&"break"!==M(L);L--);t.placement!==C&&(t.modifiersData[r]._skip=!0,t.placement=C,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function Nr(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Cr(e){return[Wn,zn,Vn,Un].some((function(t){return e[t]>=0}))}const jr={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.offset,a=void 0===o?[0,0]:o,s=er.reduce((function(e,n){return e[n]=function(e,t,n){var r=gn(e),o=[Un,Wn].indexOf(r)>=0?-1:1,a="function"==typeof n?n(Object.assign({},t,{placement:e})):n,s=a[0],i=a[1];return s=s||0,i=(i||0)*o,[Un,zn].indexOf(r)>=0?{x:i,y:s}:{x:s,y:i}}(n,t.rects,a),e}),{}),i=s[t.placement],l=i.x,c=i.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=c),t.modifiersData[r]=s}},Or={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.mainAxis,a=void 0===o||o,s=n.altAxis,i=void 0!==s&&s,l=n.boundary,c=n.rootBoundary,u=n.altBoundary,d=n.padding,f=n.tether,p=void 0===f||f,m=n.tetherOffset,v=void 0===m?0:m,h=wr(t,{boundary:l,rootBoundary:c,padding:d,altBoundary:u}),x=gn(t.placement),b=rr(t.placement),g=!b,y=Fn(x),w="x"===y?"y":"x",E=t.modifiersData.popperOffsets,N=t.rects.reference,C=t.rects.popper,j="function"==typeof v?v(Object.assign({},t.rects,{placement:t.placement})):v,O="number"==typeof j?{mainAxis:j,altAxis:j}:Object.assign({mainAxis:0,altAxis:0},j),R=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,k={x:0,y:0};if(E){if(a){var P,T="y"===y?Wn:Un,$="y"===y?Vn:zn,S="y"===y?"height":"width",D=E[y],I=D+h[T],M=D-h[$],L=p?-C[S]/2:0,A=b===Xn?N[S]:C[S],B=b===Xn?-C[S]:-N[S],F=t.elements.arrow,H=p&&F?Tn(F):{width:0,height:0},_=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},K=_[T],W=_[$],V=Hn(0,N[S],H[S]),z=g?N[S]/2-L-V-K-O.mainAxis:A-V-K-O.mainAxis,U=g?-N[S]/2+L+V+W+O.mainAxis:B+V+W+O.mainAxis,q=t.elements.arrow&&Bn(t.elements.arrow),G=q?"y"===y?q.clientTop||0:q.clientLeft||0:0,X=null!=(P=null==R?void 0:R[y])?P:0,Y=D+U-X,Z=Hn(p?jn(I,D+z-X-G):I,D,p?Cn(M,Y):M);E[y]=Z,k[y]=Z-D}if(i){var J,Q="x"===y?Wn:Un,ee="x"===y?Vn:zn,te=E[w],ne="y"===w?"height":"width",re=te+h[Q],oe=te-h[ee],ae=-1!==[Wn,Un].indexOf(x),se=null!=(J=null==R?void 0:R[w])?J:0,ie=ae?re:te-N[ne]-C[ne]-se+O.altAxis,le=ae?te+N[ne]+C[ne]-se-O.altAxis:oe,ce=p&&ae?function(e,t,n){var r=Hn(e,t,n);return r>n?n:r}(ie,te,le):Hn(p?ie:re,te,p?le:oe);E[w]=ce,k[w]=ce-te}t.modifiersData[r]=k}},requiresIfExists:["offset"]};function Rr(e,t,n){void 0===n&&(n=!1);var r,o,a=En(t),s=En(t)&&function(e){var t=e.getBoundingClientRect(),n=On(t.width)/e.offsetWidth||1,r=On(t.height)/e.offsetHeight||1;return 1!==n||1!==r}(t),i=Mn(t),l=Pn(e,s,n),c={scrollLeft:0,scrollTop:0},u={x:0,y:0};return(a||!a&&!n)&&(("body"!==Sn(t)||vr(i))&&(c=(r=t)!==yn(r)&&En(r)?{scrollLeft:(o=r).scrollLeft,scrollTop:o.scrollTop}:pr(r)),En(t)?((u=Pn(t,!0)).x+=t.clientLeft,u.y+=t.clientTop):i&&(u.x=mr(i))),{x:l.left+c.scrollLeft-u.x,y:l.top+c.scrollTop-u.y,width:l.width,height:l.height}}function kr(e){var t=new Map,n=new Set,r=[];function o(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var r=t.get(e);r&&o(r)}})),r.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||o(e)})),r}var Pr={placement:"bottom",modifiers:[],strategy:"absolute"};function Tr(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}const $r=function(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,o=t.defaultOptions,a=void 0===o?Pr:o;return function(e,t,n){void 0===n&&(n=a);var o,s,i={placement:"bottom",orderedModifiers:[],options:Object.assign({},Pr,a),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},l=[],c=!1,u={state:i,setOptions:function(n){var o="function"==typeof n?n(i.options):n;d(),i.options=Object.assign({},a,i.options,o),i.scrollParents={reference:wn(e)?xr(e):e.contextElement?xr(e.contextElement):[],popper:xr(t)};var s,c,f=function(e){var t=kr(e);return tr.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}((s=[].concat(r,i.options.modifiers),c=s.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{}),Object.keys(c).map((function(e){return c[e]}))));return i.orderedModifiers=f.filter((function(e){return e.enabled})),i.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,o=e.effect;if("function"==typeof o){var a=o({state:i,name:t,instance:u,options:r});l.push(a||function(){})}})),u.update()},forceUpdate:function(){if(!c){var e=i.elements,t=e.reference,n=e.popper;if(Tr(t,n)){i.rects={reference:Rr(t,Bn(n),"fixed"===i.options.strategy),popper:Tn(n)},i.reset=!1,i.placement=i.options.placement,i.orderedModifiers.forEach((function(e){return i.modifiersData[e.name]=Object.assign({},e.data)}));for(var r=0;r<i.orderedModifiers.length;r++)if(!0!==i.reset){var o=i.orderedModifiers[r],a=o.fn,s=o.options,l=void 0===s?{}:s,d=o.name;"function"==typeof a&&(i=a({state:i,options:l,name:d,instance:u})||i)}else i.reset=!1,r=-1}}},update:(o=function(){return new Promise((function(e){u.forceUpdate(),e(i)}))},function(){return s||(s=new Promise((function(e){Promise.resolve().then((function(){s=void 0,e(o())}))}))),s}),destroy:function(){d(),c=!0}};if(!Tr(e,t))return u;function d(){l.forEach((function(e){return e()})),l=[]}return u.setOptions(n).then((function(e){!c&&n.onFirstUpdate&&n.onFirstUpdate(e)})),u}}({defaultModifiers:[{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,a=t.modifiersData.preventOverflow,s=wr(t,{elementContext:"reference"}),i=wr(t,{altBoundary:!0}),l=Nr(s,r),c=Nr(i,o,a),u=Cr(l),d=Cr(c);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":d})}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=yr({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},sr,lr,jr,Er,Or,nr]}),Sr=["enabled","placement","strategy","modifiers"],Dr={name:"applyStyles",enabled:!1,phase:"afterWrite",fn:()=>{}},Ir={name:"ariaDescribedBy",enabled:!0,phase:"afterWrite",effect:({state:e})=>()=>{const{reference:t,popper:n}=e.elements;if("removeAttribute"in t){const e=(t.getAttribute("aria-describedby")||"").split(",").filter((e=>e.trim()!==n.id));e.length?t.setAttribute("aria-describedby",e.join(",")):t.removeAttribute("aria-describedby")}},fn:({state:e})=>{var t;const{popper:n,reference:r}=e.elements,o=null==(t=n.getAttribute("role"))?void 0:t.toLowerCase();if(n.id&&"tooltip"===o&&"setAttribute"in r){const e=r.getAttribute("aria-describedby");if(e&&-1!==e.split(",").indexOf(n.id))return;r.setAttribute("aria-describedby",e?`${e},${n.id}`:n.id)}}},Mr=[],Lr=function(e,t,r={}){let{enabled:o=!0,placement:a="bottom",strategy:s="absolute",modifiers:i=Mr}=r,l=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(r,Sr);const c=(0,n.useRef)(i),u=(0,n.useRef)(),d=(0,n.useCallback)((()=>{var e;null==(e=u.current)||e.update()}),[]),f=(0,n.useCallback)((()=>{var e;null==(e=u.current)||e.forceUpdate()}),[]),[p,m]=function(e){const t=Fe();return[e[0],(0,n.useCallback)((n=>{if(t())return e[1](n)}),[t,e[1]])]}((0,n.useState)({placement:a,update:d,forceUpdate:f,attributes:{},styles:{popper:{},arrow:{}}})),v=(0,n.useMemo)((()=>({name:"updateStateModifier",enabled:!0,phase:"write",requires:["computeStyles"],fn:({state:e})=>{const t={},n={};Object.keys(e.elements).forEach((r=>{t[r]=e.styles[r],n[r]=e.attributes[r]})),m({state:e,styles:t,attributes:n,update:d,forceUpdate:f,placement:e.placement})}})),[d,f,m]),h=(0,n.useMemo)((()=>(bn(c.current,i)||(c.current=i),c.current)),[i]);return(0,n.useEffect)((()=>{u.current&&o&&u.current.setOptions({placement:a,strategy:s,modifiers:[...h,v,Dr]})}),[s,a,v,o,h]),(0,n.useEffect)((()=>{if(o&&null!=e&&null!=t)return u.current=$r(e,t,Object.assign({},l,{placement:a,strategy:s,modifiers:[...h,Ir,v]})),()=>{null!=u.current&&(u.current.destroy(),u.current=void 0,m((e=>Object.assign({},e,{attributes:{},styles:{popper:{}}}))))}}),[o,e,t]),p};function Ar(e,t){return e.contains?e.contains(t):e.compareDocumentPosition?e===t||!!(16&e.compareDocumentPosition(t)):void 0}var Br=o(773),Fr=o.n(Br);const Hr=()=>{},_r=e=>e&&("current"in e?e.current:e),Kr={click:"mousedown",mouseup:"mousedown",pointerup:"pointerdown"},Wr=function(e,t=Hr,{disabled:r,clickTrigger:o="click"}={}){const a=(0,n.useRef)(!1),s=(0,n.useRef)(!1),i=(0,n.useCallback)((t=>{const n=_r(e);var r;Fr()(!!n,"ClickOutside captured a close event but does not have a ref to compare it to. useClickOutside(), should be passed a ref that resolves to a DOM node"),a.current=!n||!!((r=t).metaKey||r.altKey||r.ctrlKey||r.shiftKey)||!function(e){return 0===e.button}(t)||!!Ar(n,t.target)||s.current,s.current=!1}),[e]),l=Be((t=>{const n=_r(e);n&&Ar(n,t.target)?s.current=!0:s.current=!1})),c=Be((e=>{a.current||t(e)}));(0,n.useEffect)((()=>{var t,n;if(r||null==e)return;const a=C(_r(e)),s=a.defaultView||window;let u=null!=(t=s.event)?t:null==(n=s.parent)?void 0:n.event,d=null;Kr[o]&&(d=Y(a,Kr[o],l,!0));const f=Y(a,o,i,!0),p=Y(a,o,(e=>{e!==u?c(e):u=void 0}));let m=[];return"ontouchstart"in a.documentElement&&(m=[].slice.call(a.body.children).map((e=>Y(e,"mousemove",Hr)))),()=>{null==d||d(),f(),p(),m.forEach((e=>e()))}}),[e,r,o,i,l,c])};function Vr(e={}){return Array.isArray(e)?e:Object.keys(e).map((t=>(e[t].name=t,e[t])))}function zr({enabled:e,enableEvents:t,placement:n,flip:r,offset:o,fixed:a,containerPadding:s,arrowElement:i,popperConfig:l={}}){var c,u,d,f,p;const m=function(e){const t={};return Array.isArray(e)?(null==e||e.forEach((e=>{t[e.name]=e})),t):e||t}(l.modifiers);return Object.assign({},l,{placement:n,enabled:e,strategy:a?"fixed":l.strategy,modifiers:Vr(Object.assign({},m,{eventListeners:{enabled:t,options:null==(c=m.eventListeners)?void 0:c.options},preventOverflow:Object.assign({},m.preventOverflow,{options:s?Object.assign({padding:s},null==(u=m.preventOverflow)?void 0:u.options):null==(d=m.preventOverflow)?void 0:d.options}),offset:{options:Object.assign({offset:o},null==(f=m.offset)?void 0:f.options)},arrow:Object.assign({},m.arrow,{enabled:!!i,options:Object.assign({},null==(p=m.arrow)?void 0:p.options,{element:i})}),flip:Object.assign({enabled:!!r},m.flip)}))})}const Ur=["children","usePopper"],qr=()=>{};function Gr(e={}){const t=(0,n.useContext)(vn),[r,o]=Le(),a=(0,n.useRef)(!1),{flip:s,offset:i,rootCloseEvent:l,fixed:c=!1,placement:u,popperConfig:d={},enableEventListeners:f=!0,usePopper:p=!!t}=e,m=null==(null==t?void 0:t.show)?!!e.show:t.show;m&&!a.current&&(a.current=!0);const{placement:v,setMenu:h,menuElement:x,toggleElement:b}=t||{},g=Lr(b,x,zr({placement:u||v||"bottom-start",enabled:p,enableEvents:null==f?m:f,offset:i,flip:s,fixed:c,arrowElement:r,popperConfig:d})),y=Object.assign({ref:h||qr,"aria-labelledby":null==b?void 0:b.id},g.attributes.popper,{style:g.styles.popper}),w={show:m,placement:v,hasShown:a.current,toggle:null==t?void 0:t.toggle,popper:p?g:null,arrowProps:p?Object.assign({ref:o},g.attributes.arrow,{style:g.styles.arrow}):{}};return Wr(x,(e=>{null==t||t.toggle(!1,e)}),{clickTrigger:l,disabled:!m}),[y,w]}function Xr(e){let{children:t,usePopper:n=!0}=e,r=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,Ur);const[o,a]=Gr(Object.assign({},r,{usePopper:n}));return(0,p.jsx)(p.Fragment,{children:t(o,a)})}Xr.displayName="DropdownMenu";const Yr=Xr,Zr={prefix:String(Math.round(1e10*Math.random())),current:0},Jr=n.createContext(Zr),Qr=n.createContext(!1);function eo(e){let t=(0,n.useContext)(Jr),r=oo(t===Zr),[o,a]=(0,n.useState)(!0),s=(0,n.useMemo)((()=>({prefix:t===Zr?"":`${t.prefix}-${r}`,current:0})),[t,r]);return"undefined"!=typeof document&&(0,n.useLayoutEffect)((()=>{a(!1)}),[]),n.createElement(Jr.Provider,{value:s},n.createElement(Qr.Provider,{value:o},e.children))}let to=!1,no=Boolean("undefined"!=typeof window&&window.document&&window.document.createElement),ro=new WeakMap;function oo(e=!1){let t=(0,n.useContext)(Jr),r=(0,n.useRef)(null);if(null===r.current&&!e){var o,a;let e=null===(a=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)||void 0===a||null===(o=a.ReactCurrentOwner)||void 0===o?void 0:o.current;if(e){let n=ro.get(e);null==n?ro.set(e,{id:t.current,state:e.memoizedState}):e.memoizedState!==n.state&&(t.current=n.id,ro.delete(e))}r.current=++t.current}return r.current}const ao="function"==typeof n.useId?function(e){let t=n.useId(),[r]=(0,n.useState)("function"==typeof n.useSyncExternalStore?n.useSyncExternalStore(lo,so,io):(0,n.useContext)(Qr));return e||`${r?"react-aria":`react-aria${Zr.prefix}`}-${t}`}:function(e){let t=(0,n.useContext)(Jr);t!==Zr||no||console.warn("When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server.");let r=oo(!!e),o=`react-aria${t.prefix}`;return e||`${o}-${r}`};function so(){return!1}function io(){return!0}function lo(e){return()=>{}}const co=e=>{var t;return"menu"===(null==(t=e.getAttribute("role"))?void 0:t.toLowerCase())},uo=()=>{};function fo(){const e=ao(),{show:t=!1,toggle:r=uo,setToggle:o,menuElement:a}=(0,n.useContext)(vn)||{},s=(0,n.useCallback)((e=>{r(!t,e)}),[t,r]),i={id:e,ref:o||uo,onClick:s,"aria-expanded":!!t};return a&&co(a)&&(i["aria-haspopup"]=!0),[i,{show:t,toggle:r}]}function po({children:e}){const[t,n]=fo();return(0,p.jsx)(p.Fragment,{children:e(t,n)})}po.displayName="DropdownToggle";const mo=po,vo=(e,t=null)=>null!=e?String(e):t||null,ho=n.createContext(null),xo=n.createContext(null);xo.displayName="NavContext";const bo=xo,go="data-rr-ui-";function yo(e){return`${go}${e}`}const wo=["eventKey","disabled","onClick","active","as"];function Eo({key:e,href:t,active:r,disabled:o,onClick:a}){const s=(0,n.useContext)(ho),i=(0,n.useContext)(bo),{activeKey:l}=i||{},c=vo(e,t),u=null==r&&null!=e?vo(l)===c:r;return[{onClick:Be((e=>{o||(null==a||a(e),s&&!e.isPropagationStopped()&&s(c,e))})),"aria-disabled":o||void 0,"aria-selected":u,[yo("dropdown-item")]:""},{isActive:u}]}const No=n.forwardRef(((e,t)=>{let{eventKey:n,disabled:r,onClick:o,active:a,as:s=Ue}=e,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,wo);const[l]=Eo({key:n,href:i.href,disabled:r,onClick:o,active:a});return(0,p.jsx)(s,Object.assign({},i,{ref:t},l))}));No.displayName="DropdownItem";const Co=No,jo=(0,n.createContext)(V?window:void 0);function Oo(){return(0,n.useContext)(jo)}function Ro(){const e=mn(),t=(0,n.useRef)(null),r=(0,n.useCallback)((n=>{t.current=n,e()}),[e]);return[t,r]}function ko({defaultShow:e,show:t,onSelect:r,onToggle:o,itemSelector:a=`* [${yo("dropdown-item")}]`,focusFirstItemOnShow:s,placement:i="bottom-start",children:l}){const c=Oo(),[u,d]=pn(t,e,o),[f,m]=Ro(),v=f.current,[h,x]=Ro(),b=h.current,g=He(u),y=(0,n.useRef)(null),w=(0,n.useRef)(!1),E=(0,n.useContext)(ho),N=(0,n.useCallback)(((e,t,n=(null==t?void 0:t.type))=>{d(e,{originalEvent:t,source:n})}),[d]),C=Be(((e,t)=>{null==r||r(e,t),N(!1,t,"select"),t.isPropagationStopped()||null==E||E(e,t)})),j=(0,n.useMemo)((()=>({toggle:N,placement:i,show:u,menuElement:v,toggleElement:b,setMenu:m,setToggle:x})),[N,i,u,v,b,m,x]);v&&g&&!u&&(w.current=v.contains(v.ownerDocument.activeElement));const O=Be((()=>{b&&b.focus&&b.focus()})),R=Be((()=>{const e=y.current;let t=s;if(null==t&&(t=!(!f.current||!co(f.current))&&"keyboard"),!1===t||"keyboard"===t&&!/^key.+$/.test(e))return;const n=fn(f.current,a)[0];n&&n.focus&&n.focus()}));(0,n.useEffect)((()=>{u?R():w.current&&(w.current=!1,O())}),[u,w,O,R]),(0,n.useEffect)((()=>{y.current=null}));const k=(e,t)=>{if(!f.current)return null;const n=fn(f.current,a);let r=n.indexOf(e)+t;return r=Math.max(0,Math.min(r,n.length)),n[r]};return function(e,t,r,o=!1){const a=Be(r);(0,n.useEffect)((()=>{const n="function"==typeof e?e():e;return n.addEventListener(t,a,o),()=>n.removeEventListener(t,a,o)}),[e])}((0,n.useCallback)((()=>c.document),[c]),"keydown",(e=>{var t,n;const{key:r}=e,o=e.target,a=null==(t=f.current)?void 0:t.contains(o),s=null==(n=h.current)?void 0:n.contains(o);if(/input|textarea/i.test(o.tagName)&&(" "===r||"Escape"!==r&&a||"Escape"===r&&"search"===o.type))return;if(!a&&!s)return;if(!("Tab"!==r||f.current&&u))return;y.current=e.type;const i={originalEvent:e,source:e.type};switch(r){case"ArrowUp":{const t=k(o,-1);return t&&t.focus&&t.focus(),void e.preventDefault()}case"ArrowDown":if(e.preventDefault(),u){const e=k(o,1);e&&e.focus&&e.focus()}else d(!0,i);return;case"Tab":G(o.ownerDocument,"keyup",(e=>{var t;("Tab"!==e.key||e.target)&&null!=(t=f.current)&&t.contains(e.target)||d(!1,i)}),{once:!0});break;case"Escape":"Escape"===r&&(e.preventDefault(),e.stopPropagation()),d(!1,i)}})),(0,p.jsx)(ho.Provider,{value:C,children:(0,p.jsx)(vn.Provider,{value:j,children:l})})}jo.Provider,ko.displayName="Dropdown",ko.Menu=Yr,ko.Toggle=mo,ko.Item=Co;const Po=ko,To=n.createContext({});To.displayName="DropdownContext";const $o=To,So=n.forwardRef((({className:e,bsPrefix:n,as:r="hr",role:o="separator",...a},s)=>(n=g(n,"dropdown-divider"),(0,p.jsx)(r,{ref:s,className:t()(e,n),role:o,...a}))));So.displayName="DropdownDivider";const Do=So,Io=n.forwardRef((({className:e,bsPrefix:n,as:r="div",role:o="heading",...a},s)=>(n=g(n,"dropdown-header"),(0,p.jsx)(r,{ref:s,className:t()(e,n),role:o,...a}))));Io.displayName="DropdownHeader";const Mo=Io,Lo=n.forwardRef((({bsPrefix:e,className:n,eventKey:r,disabled:o=!1,onClick:a,active:s,as:i=Xe,...l},c)=>{const u=g(e,"dropdown-item"),[d,f]=Eo({key:r,href:l.href,disabled:o,onClick:a,active:s});return(0,p.jsx)(i,{...l,...d,ref:c,className:t()(n,u,f.isActive&&"active",o&&"disabled")})}));Lo.displayName="DropdownItem";const Ao=Lo,Bo=n.forwardRef((({className:e,bsPrefix:n,as:r="span",...o},a)=>(n=g(n,"dropdown-item-text"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));Bo.displayName="DropdownItemText";const Fo=Bo,Ho=void 0!==o.g&&o.g.navigator&&"ReactNative"===o.g.navigator.product,_o="undefined"!=typeof document||Ho?n.useLayoutEffect:n.useEffect,Ko=n.createContext(null);Ko.displayName="InputGroupContext";const Wo=Ko,Vo=n.createContext(null);Vo.displayName="NavbarContext";const zo=Vo;function Uo(e,t){return e}function qo(e,t,n){let r=e?n?"bottom-start":"bottom-end":n?"bottom-end":"bottom-start";return"up"===t?r=e?n?"top-start":"top-end":n?"top-end":"top-start":"end"===t?r=e?n?"left-end":"right-end":n?"left-start":"right-start":"start"===t?r=e?n?"right-end":"left-end":n?"right-start":"left-start":"down-centered"===t?r="bottom":"up-centered"===t&&(r="top"),r}const Go=n.forwardRef((({bsPrefix:e,className:r,align:o,rootCloseEvent:a,flip:s=!0,show:i,renderOnMount:l,as:c="div",popperConfig:u,variant:d,...f},m)=>{let v=!1;const h=(0,n.useContext)(zo),x=g(e,"dropdown-menu"),{align:b,drop:y,isRTL:w}=(0,n.useContext)($o);o=o||b;const E=(0,n.useContext)(Wo),N=[];if(o)if("object"==typeof o){const e=Object.keys(o);if(e.length){const t=e[0],n=o[t];v="start"===n,N.push(`${x}-${t}-${n}`)}}else"end"===o&&(v=!0);const C=qo(v,y,w),[j,{hasShown:O,popper:R,show:k,toggle:P}]=Gr({flip:s,rootCloseEvent:a,show:i,usePopper:!h&&0===N.length,offset:[0,2],popperConfig:u,placement:C});if(j.ref=re(Uo(m),j.ref),_o((()=>{k&&(null==R||R.update())}),[k]),!O&&!l&&!E)return null;"string"!=typeof c&&(j.show=k,j.close=()=>null==P?void 0:P(!1),j.align=o);let T=f.style;return null!=R&&R.placement&&(T={...f.style,...j.style},f["x-placement"]=R.placement),(0,p.jsx)(c,{...f,...j,style:T,...(N.length||h)&&{"data-bs-popper":"static"},className:t()(r,x,k&&"show",v&&`${x}-end`,d&&`${x}-${d}`,...N)})}));Go.displayName="DropdownMenu";const Xo=Go,Yo=n.forwardRef((({bsPrefix:e,split:r,className:o,childBsPrefix:a,as:s=ht,...i},l)=>{const c=g(e,"dropdown-toggle"),u=(0,n.useContext)(vn);void 0!==a&&(i.bsPrefix=a);const[d]=fo();return d.ref=re(d.ref,Uo(l)),(0,p.jsx)(s,{className:t()(o,c,r&&`${c}-split`,(null==u?void 0:u.show)&&"show"),...d,...i})}));Yo.displayName="DropdownToggle";const Zo=Yo,Jo=n.forwardRef(((e,r)=>{const{bsPrefix:o,drop:a="down",show:s,className:i,align:l="start",onSelect:c,onToggle:u,focusFirstItemOnShow:d,as:m="div",navbar:v,autoClose:h=!0,...x}=f(e,{show:"onToggle"}),b=(0,n.useContext)(Wo),y=g(o,"dropdown"),w=E(),N=$e(((e,t)=>{var n,r;(null==(n=t.originalEvent)||null==(n=n.target)?void 0:n.classList.contains("dropdown-toggle"))&&"mousedown"===t.source||(t.originalEvent.currentTarget!==document||"keydown"===t.source&&"Escape"!==t.originalEvent.key||(t.source="rootClose"),r=t.source,(!1===h?"click"===r:"inside"===h?"rootClose"!==r:"outside"!==h||"select"!==r)&&(null==u||u(e,t)))})),C=qo("end"===l,a,w),j=(0,n.useMemo)((()=>({align:l,drop:a,isRTL:w})),[l,a,w]),O={down:y,"down-centered":`${y}-center`,up:"dropup","up-centered":"dropup-center dropup",end:"dropend",start:"dropstart"};return(0,p.jsx)($o.Provider,{value:j,children:(0,p.jsx)(Po,{placement:C,show:s,onSelect:c,onToggle:N,focusFirstItemOnShow:d,itemSelector:`.${y}-item:not(.disabled):not(:disabled)`,children:b?x.children:(0,p.jsx)(m,{...x,ref:r,className:t()(i,s&&"show",O[a])})})})}));Jo.displayName="Dropdown";const Qo=Object.assign(Jo,{Toggle:Zo,Menu:Xo,Item:Ao,ItemText:Fo,Divider:Do,Header:Mo}),ea=nt().oneOf(["start","end"]),ta=nt().oneOfType([ea,nt().shape({sm:ea}),nt().shape({md:ea}),nt().shape({lg:ea}),nt().shape({xl:ea}),nt().shape({xxl:ea}),nt().object]),na={id:nt().string,href:nt().string,onClick:nt().func,title:nt().node.isRequired,disabled:nt().bool,align:ta,menuRole:nt().string,renderMenuOnMount:nt().bool,rootCloseEvent:nt().string,menuVariant:nt().oneOf(["dark"]),flip:nt().bool,bsPrefix:nt().string,variant:nt().string,size:nt().string},ra=n.forwardRef((({title:e,children:t,bsPrefix:n,rootCloseEvent:r,variant:o,size:a,menuRole:s,renderMenuOnMount:i,disabled:l,href:c,id:u,menuVariant:d,flip:f,...m},v)=>(0,p.jsxs)(Qo,{ref:v,...m,children:[(0,p.jsx)(Zo,{id:u,href:c,size:a,variant:o,disabled:l,childBsPrefix:n,children:e}),(0,p.jsx)(Xo,{role:s,renderOnMount:i,rootCloseEvent:r,variant:d,flip:f,children:t})]})));ra.displayName="DropdownButton",ra.propTypes=na;const oa=ra,aa={bsPrefix:nt().string,fluid:nt().bool,rounded:nt().bool,roundedCircle:nt().bool,thumbnail:nt().bool},sa=n.forwardRef((({bsPrefix:e,className:n,fluid:r=!1,rounded:o=!1,roundedCircle:a=!1,thumbnail:s=!1,...i},l)=>(e=g(e,"img"),(0,p.jsx)("img",{ref:l,...i,className:t()(n,r&&`${e}-fluid`,o&&"rounded",a&&"rounded-circle",s&&`${e}-thumbnail`)}))));sa.displayName="Image";const ia=sa,la=n.forwardRef((({className:e,fluid:n=!0,...r},o)=>(0,p.jsx)(ia,{ref:o,...r,fluid:n,className:t()(e,"figure-img")})));la.displayName="FigureImage",la.propTypes=aa;const ca=la,ua=n.forwardRef((({className:e,bsPrefix:n,as:r="figcaption",...o},a)=>(n=g(n,"figure-caption"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));ua.displayName="FigureCaption";const da=ua,fa=n.forwardRef((({className:e,bsPrefix:n,as:r="figure",...o},a)=>(n=g(n,"figure"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));fa.displayName="Figure";const pa=Object.assign(fa,{Image:ca,Caption:da}),ma={type:nt().string,tooltip:nt().bool,as:nt().elementType},va=n.forwardRef((({as:e="div",className:n,type:r="valid",tooltip:o=!1,...a},s)=>(0,p.jsx)(e,{...a,ref:s,className:t()(n,`${r}-${o?"tooltip":"feedback"}`)})));va.displayName="Feedback",va.propTypes=ma;const ha=va,xa=n.createContext({}),ba=n.forwardRef((({id:e,bsPrefix:r,className:o,type:a="checkbox",isValid:s=!1,isInvalid:i=!1,as:l="input",...c},u)=>{const{controlId:d}=(0,n.useContext)(xa);return r=g(r,"form-check-input"),(0,p.jsx)(l,{...c,ref:u,type:a,id:e||d,className:t()(o,r,s&&"is-valid",i&&"is-invalid")})}));ba.displayName="FormCheckInput";const ga=ba,ya=n.forwardRef((({bsPrefix:e,className:r,htmlFor:o,...a},s)=>{const{controlId:i}=(0,n.useContext)(xa);return e=g(e,"form-check-label"),(0,p.jsx)("label",{...a,ref:s,htmlFor:o||i,className:t()(r,e)})}));ya.displayName="FormCheckLabel";const wa=ya,Ea=n.forwardRef((({id:e,bsPrefix:r,bsSwitchPrefix:o,inline:a=!1,reverse:s=!1,disabled:i=!1,isValid:l=!1,isInvalid:c=!1,feedbackTooltip:u=!1,feedback:d,feedbackType:f,className:m,style:v,title:h="",type:x="checkbox",label:b,children:y,as:w="input",...E},N)=>{r=g(r,"form-check"),o=g(o,"form-switch");const{controlId:C}=(0,n.useContext)(xa),j=(0,n.useMemo)((()=>({controlId:e||C})),[C,e]),O=!y&&null!=b&&!1!==b||function(e,t){return n.Children.toArray(e).some((e=>n.isValidElement(e)&&e.type===t))}(y,wa),R=(0,p.jsx)(ga,{...E,type:"switch"===x?"checkbox":x,ref:N,isValid:l,isInvalid:c,disabled:i,as:w});return(0,p.jsx)(xa.Provider,{value:j,children:(0,p.jsx)("div",{style:v,className:t()(m,O&&r,a&&`${r}-inline`,s&&`${r}-reverse`,"switch"===x&&o),children:y||(0,p.jsxs)(p.Fragment,{children:[R,O&&(0,p.jsx)(wa,{title:h,children:b}),d&&(0,p.jsx)(ha,{type:f,tooltip:u,children:d})]})})})}));Ea.displayName="FormCheck";const Na=Object.assign(Ea,{Input:ga,Label:wa}),Ca=n.forwardRef((({bsPrefix:e,type:r,size:o,htmlSize:a,id:s,className:i,isValid:l=!1,isInvalid:c=!1,plaintext:u,readOnly:d,as:f="input",...m},v)=>{const{controlId:h}=(0,n.useContext)(xa);return e=g(e,"form-control"),(0,p.jsx)(f,{...m,type:r,size:a,ref:v,readOnly:d,id:s||h,className:t()(i,u?`${e}-plaintext`:e,o&&`${e}-${o}`,"color"===r&&`${e}-color`,l&&"is-valid",c&&"is-invalid")})}));Ca.displayName="FormControl";const ja=Object.assign(Ca,{Feedback:ha}),Oa=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=g(n,"form-floating"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));Oa.displayName="FormFloating";const Ra=Oa,ka=n.forwardRef((({controlId:e,as:t="div",...r},o)=>{const a=(0,n.useMemo)((()=>({controlId:e})),[e]);return(0,p.jsx)(xa.Provider,{value:a,children:(0,p.jsx)(t,{...r,ref:o})})}));ka.displayName="FormGroup";const Pa=ka,Ta=n.forwardRef((({as:e="label",bsPrefix:r,column:o=!1,visuallyHidden:a=!1,className:s,htmlFor:i,...l},c)=>{const{controlId:u}=(0,n.useContext)(xa);r=g(r,"form-label");let d="col-form-label";"string"==typeof o&&(d=`${d} ${d}-${o}`);const f=t()(s,r,a&&"visually-hidden",o&&d);return i=i||u,o?(0,p.jsx)(ln,{ref:c,as:"label",className:f,htmlFor:i,...l}):(0,p.jsx)(e,{ref:c,className:f,htmlFor:i,...l})}));Ta.displayName="FormLabel";const $a=Ta,Sa=n.forwardRef((({bsPrefix:e,className:r,id:o,...a},s)=>{const{controlId:i}=(0,n.useContext)(xa);return e=g(e,"form-range"),(0,p.jsx)("input",{...a,type:"range",ref:s,className:t()(r,e),id:o||i})}));Sa.displayName="FormRange";const Da=Sa,Ia=n.forwardRef((({bsPrefix:e,size:r,htmlSize:o,className:a,isValid:s=!1,isInvalid:i=!1,id:l,...c},u)=>{const{controlId:d}=(0,n.useContext)(xa);return e=g(e,"form-select"),(0,p.jsx)("select",{...c,size:o,ref:u,className:t()(a,e,r&&`${e}-${r}`,s&&"is-valid",i&&"is-invalid"),id:l||d})}));Ia.displayName="FormSelect";const Ma=Ia,La=n.forwardRef((({bsPrefix:e,className:n,as:r="small",muted:o,...a},s)=>(e=g(e,"form-text"),(0,p.jsx)(r,{...a,ref:s,className:t()(n,e,o&&"text-muted")}))));La.displayName="FormText";const Aa=La,Ba=n.forwardRef(((e,t)=>(0,p.jsx)(Na,{...e,ref:t,type:"switch"})));Ba.displayName="Switch";const Fa=Object.assign(Ba,{Input:Na.Input,Label:Na.Label}),Ha=n.forwardRef((({bsPrefix:e,className:n,children:r,controlId:o,label:a,...s},i)=>(e=g(e,"form-floating"),(0,p.jsxs)(Pa,{ref:i,className:t()(n,e),controlId:o,...s,children:[r,(0,p.jsx)("label",{htmlFor:o,children:a})]}))));Ha.displayName="FloatingLabel";const _a=Ha,Ka={_ref:nt().any,validated:nt().bool,as:nt().elementType},Wa=n.forwardRef((({className:e,validated:n,as:r="form",...o},a)=>(0,p.jsx)(r,{...o,ref:a,className:t()(e,n&&"was-validated")})));Wa.displayName="Form",Wa.propTypes=Ka;const Va=Object.assign(Wa,{Group:Pa,Control:ja,Floating:Ra,Check:Na,Switch:Fa,Label:$a,Text:Aa,Range:Da,Select:Ma,FloatingLabel:_a}),za=n.forwardRef((({className:e,bsPrefix:n,as:r="span",...o},a)=>(n=g(n,"input-group-text"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));za.displayName="InputGroupText";const Ua=za,qa=n.forwardRef((({bsPrefix:e,size:r,hasValidation:o,className:a,as:s="div",...i},l)=>{e=g(e,"input-group");const c=(0,n.useMemo)((()=>({})),[]);return(0,p.jsx)(Wo.Provider,{value:c,children:(0,p.jsx)(s,{ref:l,...i,className:t()(a,e,r&&`${e}-${r}`,o&&"has-validation")})})}));qa.displayName="InputGroup";const Ga=Object.assign(qa,{Text:Ua,Radio:e=>(0,p.jsx)(Ua,{children:(0,p.jsx)(ga,{type:"radio",...e})}),Checkbox:e=>(0,p.jsx)(Ua,{children:(0,p.jsx)(ga,{type:"checkbox",...e})})}),Xa=e=>e&&"function"!=typeof e?t=>{e.current=t}:e,Ya=function(e,t){return(0,n.useMemo)((()=>function(e,t){const n=Xa(e),r=Xa(t);return e=>{n&&n(e),r&&r(e)}}(e,t)),[e,t])},Za=n.createContext(null),Ja=["as","active","eventKey"];function Qa({key:e,onClick:t,active:r,id:o,role:a,disabled:s}){const i=(0,n.useContext)(ho),l=(0,n.useContext)(bo),c=(0,n.useContext)(Za);let u=r;const d={role:a};if(l){a||"tablist"!==l.role||(d.role="tab");const t=l.getControllerId(null!=e?e:null),n=l.getControlledId(null!=e?e:null);d[yo("event-key")]=e,d.id=t||o,u=null==r&&null!=e?l.activeKey===e:r,!u&&(null!=c&&c.unmountOnExit||null!=c&&c.mountOnEnter)||(d["aria-controls"]=n)}return"tab"===d.role&&(d["aria-selected"]=u,u||(d.tabIndex=-1),s&&(d.tabIndex=-1,d["aria-disabled"]=!0)),d.onClick=Be((n=>{s||(null==t||t(n),null!=e&&i&&!n.isPropagationStopped()&&i(e,n))})),[d,{isActive:u}]}const es=n.forwardRef(((e,t)=>{let{as:n=Ue,active:r,eventKey:o}=e,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,Ja);const[s,i]=Qa(Object.assign({key:vo(o,a.href),active:r},a));return s[yo("active")]=i.isActive,(0,p.jsx)(n,Object.assign({},a,s,{ref:t}))}));es.displayName="NavItem";const ts=es,ns=["as","onSelect","activeKey","role","onKeyDown"],rs=()=>{},os=yo("event-key"),as=n.forwardRef(((e,t)=>{let{as:r="div",onSelect:o,activeKey:a,role:s,onKeyDown:i}=e,l=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,ns);const c=mn(),u=(0,n.useRef)(!1),d=(0,n.useContext)(ho),f=(0,n.useContext)(Za);let m,v;f&&(s=s||"tablist",a=f.activeKey,m=f.getControlledId,v=f.getControllerId);const h=(0,n.useRef)(null),x=e=>{const t=h.current;if(!t)return null;const n=fn(t,`[${os}]:not([aria-disabled=true])`),r=t.querySelector("[aria-selected=true]");if(!r||r!==document.activeElement)return null;const o=n.indexOf(r);if(-1===o)return null;let a=o+e;return a>=n.length&&(a=0),a<0&&(a=n.length-1),n[a]},b=(e,t)=>{null!=e&&(null==o||o(e,t),null==d||d(e,t))};(0,n.useEffect)((()=>{if(h.current&&u.current){const e=h.current.querySelector(`[${os}][aria-selected=true]`);null==e||e.focus()}u.current=!1}));const g=Ya(t,h);return(0,p.jsx)(ho.Provider,{value:b,children:(0,p.jsx)(bo.Provider,{value:{role:s,activeKey:vo(a),getControlledId:m||rs,getControllerId:v||rs},children:(0,p.jsx)(r,Object.assign({},l,{onKeyDown:e=>{if(null==i||i(e),!f)return;let t;switch(e.key){case"ArrowLeft":case"ArrowUp":t=x(-1);break;case"ArrowRight":case"ArrowDown":t=x(1);break;default:return}t&&(e.preventDefault(),b(t.dataset["rrUiEventKey"]||null,e),u.current=!0,c())},ref:g,role:s}))})})}));as.displayName="Nav";const ss=Object.assign(as,{Item:ts}),is=n.forwardRef((({bsPrefix:e,active:n,disabled:r,eventKey:o,className:a,variant:s,action:i,as:l,...c},u)=>{e=g(e,"list-group-item");const[d,f]=Qa({key:vo(o,c.href),active:n,...c}),m=$e((e=>{if(r)return e.preventDefault(),void e.stopPropagation();d.onClick(e)}));r&&void 0===c.tabIndex&&(c.tabIndex=-1,c["aria-disabled"]=!0);const v=l||(i?c.href?"a":"button":"div");return(0,p.jsx)(v,{ref:u,...c,...d,onClick:m,className:t()(a,e,f.isActive&&"active",r&&"disabled",s&&`${e}-${s}`,i&&`${e}-action`)})}));is.displayName="ListGroupItem";const ls=is,cs=n.forwardRef(((e,n)=>{const{className:r,bsPrefix:o,variant:a,horizontal:s,numbered:i,as:l="div",...c}=f(e,{activeKey:"onSelect"}),u=g(o,"list-group");let d;return s&&(d=!0===s?"horizontal":`horizontal-${s}`),(0,p.jsx)(ss,{ref:n,...c,as:l,className:t()(r,u,a&&`${u}-${a}`,d&&`${u}-${d}`,i&&`${u}-numbered`)})}));cs.displayName="ListGroup";const us=Object.assign(cs,{Item:ls});var ds;function fs(e){if((!ds&&0!==ds||e)&&V){var t=document.createElement("div");t.style.position="absolute",t.style.top="-9999px",t.style.width="50px",t.style.height="50px",t.style.overflow="scroll",document.body.appendChild(t),ds=t.offsetWidth-t.clientWidth,document.body.removeChild(t)}return ds}function ps(e){void 0===e&&(e=C());try{var t=e.activeElement;return t&&t.nodeName?t:null}catch(t){return e.body}}const ms=yo("modal-open"),vs=class{constructor({ownerDocument:e,handleContainerOverflow:t=!0,isRTL:n=!1}={}){this.handleContainerOverflow=t,this.isRTL=n,this.modals=[],this.ownerDocument=e}getScrollbarWidth(){return function(e=document){const t=e.defaultView;return Math.abs(t.innerWidth-e.documentElement.clientWidth)}(this.ownerDocument)}getElement(){return(this.ownerDocument||document).body}setModalAttributes(e){}removeModalAttributes(e){}setContainerStyle(e){const t={overflow:"hidden"},n=this.isRTL?"paddingLeft":"paddingRight",r=this.getElement();e.style={overflow:r.style.overflow,[n]:r.style[n]},e.scrollBarWidth&&(t[n]=`${parseInt(P(r,n)||"0",10)+e.scrollBarWidth}px`),r.setAttribute(ms,""),P(r,t)}reset(){[...this.modals].forEach((e=>this.remove(e)))}removeContainerStyle(e){const t=this.getElement();t.removeAttribute(ms),Object.assign(t.style,e.style)}add(e){let t=this.modals.indexOf(e);return-1!==t||(t=this.modals.length,this.modals.push(e),this.setModalAttributes(e),0!==t||(this.state={scrollBarWidth:this.getScrollbarWidth(),style:{}},this.handleContainerOverflow&&this.setContainerStyle(this.state))),t}remove(e){const t=this.modals.indexOf(e);-1!==t&&(this.modals.splice(t,1),!this.modals.length&&this.handleContainerOverflow&&this.removeContainerStyle(this.state),this.removeModalAttributes(e))}isTopModal(e){return!!this.modals.length&&this.modals[this.modals.length-1]===e}},hs=(e,t)=>V?null==e?(t||C()).body:("function"==typeof e&&(e=e()),e&&"current"in e&&(e=e.current),e&&("nodeType"in e||e.getBoundingClientRect)?e:null):null;function xs(e,t){const r=Oo(),[o,a]=(0,n.useState)((()=>hs(e,null==r?void 0:r.document)));if(!o){const t=hs(e);t&&a(t)}return(0,n.useEffect)((()=>{t&&o&&t(o)}),[t,o]),(0,n.useEffect)((()=>{const t=hs(e);t!==o&&a(t)}),[e,o]),o}const bs=function({children:e,in:t,onExited:r,mountOnEnter:o,unmountOnExit:a}){const s=(0,n.useRef)(null),i=(0,n.useRef)(t),l=Be(r);(0,n.useEffect)((()=>{t?i.current=!0:l(s.current)}),[t,l]);const c=Ya(s,W(e)),u=(0,n.cloneElement)(e,{ref:c});return t?u:a||!i.current&&o?null:u},gs=["onEnter","onEntering","onEntered","onExit","onExiting","onExited","addEndListener","children"],ys=["component"],ws=n.forwardRef(((e,t)=>{let{component:r}=e;const o=function(e){let{onEnter:t,onEntering:r,onEntered:o,onExit:a,onExiting:s,onExited:i,addEndListener:l,children:c}=e,u=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,gs);const d=(0,n.useRef)(null),f=Ya(d,W(c)),p=e=>t=>{e&&d.current&&e(d.current,t)},m=(0,n.useCallback)(p(t),[t]),v=(0,n.useCallback)(p(r),[r]),h=(0,n.useCallback)(p(o),[o]),x=(0,n.useCallback)(p(a),[a]),b=(0,n.useCallback)(p(s),[s]),g=(0,n.useCallback)(p(i),[i]),y=(0,n.useCallback)(p(l),[l]);return Object.assign({},u,{nodeRef:d},t&&{onEnter:m},r&&{onEntering:v},o&&{onEntered:h},a&&{onExit:x},s&&{onExiting:b},i&&{onExited:g},l&&{addEndListener:y},{children:"function"==typeof c?(e,t)=>c(e,Object.assign({},t,{ref:f})):(0,n.cloneElement)(c,{ref:f})})}(function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,ys));return(0,p.jsx)(r,Object.assign({ref:t},o))}));function Es({children:e,in:t,onExited:r,onEntered:o,transition:a}){const[s,i]=(0,n.useState)(!t);t&&s&&i(!1);const l=function({in:e,onTransition:t}){const r=(0,n.useRef)(null),o=(0,n.useRef)(!0),a=Be(t);return Ke((()=>{if(!r.current)return;let t=!1;return a({in:e,element:r.current,initial:o.current,isStale:()=>t}),()=>{t=!0}}),[e,a]),Ke((()=>(o.current=!1,()=>{o.current=!0})),[]),r}({in:!!t,onTransition:e=>{Promise.resolve(a(e)).then((()=>{e.isStale()||(e.in?null==o||o(e.element,e.initial):(i(!0),null==r||r(e.element)))}),(t=>{throw e.in||i(!0),t}))}}),c=Ya(l,W(e));return s&&!t?null:(0,n.cloneElement)(e,{ref:c})}function Ns(e,t,n){return e?(0,p.jsx)(ws,Object.assign({},n,{component:e})):t?(0,p.jsx)(Es,Object.assign({},n,{transition:t})):(0,p.jsx)(bs,Object.assign({},n))}const Cs=["show","role","className","style","children","backdrop","keyboard","onBackdropClick","onEscapeKeyDown","transition","runTransition","backdropTransition","runBackdropTransition","autoFocus","enforceFocus","restoreFocus","restoreFocusOptions","renderDialog","renderBackdrop","manager","container","onShow","onHide","onExit","onExited","onExiting","onEnter","onEntering","onEntered"];let js;const Os=(0,n.forwardRef)(((e,t)=>{let{show:r=!1,role:o="dialog",className:a,style:s,children:i,backdrop:l=!0,keyboard:c=!0,onBackdropClick:u,onEscapeKeyDown:d,transition:f,runTransition:m,backdropTransition:v,runBackdropTransition:h,autoFocus:x=!0,enforceFocus:b=!0,restoreFocus:g=!0,restoreFocusOptions:y,renderDialog:w,renderBackdrop:E=e=>(0,p.jsx)("div",Object.assign({},e)),manager:N,container:C,onShow:j,onHide:O=()=>{},onExit:R,onExited:k,onExiting:P,onEnter:T,onEntering:$,onEntered:D}=e,I=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,Cs);const M=Oo(),L=xs(C),A=function(e){const t=Oo(),r=e||function(e){return js||(js=new vs({ownerDocument:null==e?void 0:e.document})),js}(t),o=(0,n.useRef)({dialog:null,backdrop:null});return Object.assign(o.current,{add:()=>r.add(o.current),remove:()=>r.remove(o.current),isTopModal:()=>r.isTopModal(o.current),setDialogRef:(0,n.useCallback)((e=>{o.current.dialog=e}),[]),setBackdropRef:(0,n.useCallback)((e=>{o.current.backdrop=e}),[])})}(N),B=Fe(),F=He(r),[H,_]=(0,n.useState)(!r),W=(0,n.useRef)(null);(0,n.useImperativeHandle)(t,(()=>A),[A]),V&&!F&&r&&(W.current=ps(null==M?void 0:M.document)),r&&H&&_(!1);const z=Be((()=>{if(A.add(),J.current=Y(document,"keydown",X),Z.current=Y(document,"focus",(()=>setTimeout(q)),!0),j&&j(),x){var e,t;const n=ps(null!=(e=null==(t=A.dialog)?void 0:t.ownerDocument)?e:null==M?void 0:M.document);A.dialog&&n&&!Ar(A.dialog,n)&&(W.current=n,A.dialog.focus())}})),U=Be((()=>{var e;A.remove(),null==J.current||J.current(),null==Z.current||Z.current(),g&&(null==(e=W.current)||null==e.focus||e.focus(y),W.current=null)}));(0,n.useEffect)((()=>{r&&L&&z()}),[r,L,z]),(0,n.useEffect)((()=>{H&&U()}),[H,U]),function(e){const t=function(e){const t=(0,n.useRef)(e);return t.current=e,t}(e);(0,n.useEffect)((()=>()=>t.current()),[])}((()=>{U()}));const q=Be((()=>{if(!b||!B()||!A.isTopModal())return;const e=ps(null==M?void 0:M.document);A.dialog&&e&&!Ar(A.dialog,e)&&A.dialog.focus()})),G=Be((e=>{e.target===e.currentTarget&&(null==u||u(e),!0===l&&O())})),X=Be((e=>{c&&K(e)&&A.isTopModal()&&(null==d||d(e),e.defaultPrevented||O())})),Z=(0,n.useRef)(),J=(0,n.useRef)();if(!L)return null;const Q=Object.assign({role:o,ref:A.setDialogRef,"aria-modal":"dialog"===o||void 0},I,{style:s,className:a,tabIndex:-1});let ee=w?w(Q):(0,p.jsx)("div",Object.assign({},Q,{children:n.cloneElement(i,{role:"document"})}));ee=Ns(f,m,{unmountOnExit:!0,mountOnEnter:!0,appear:!0,in:!!r,onExit:R,onExiting:P,onExited:(...e)=>{_(!0),null==k||k(...e)},onEnter:T,onEntering:$,onEntered:D,children:ee});let te=null;return l&&(te=E({ref:A.setBackdropRef,onClick:G}),te=Ns(v,h,{in:!!r,appear:!0,mountOnEnter:!0,unmountOnExit:!0,children:te})),(0,p.jsx)(p.Fragment,{children:S().createPortal((0,p.jsxs)(p.Fragment,{children:[te,ee]}),L)})}));Os.displayName="Modal";const Rs=Object.assign(Os,{Manager:vs});function ks(e,t){return e.classList?!!t&&e.classList.contains(t):-1!==(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+t+" ")}function Ps(e,t){return e.replace(new RegExp("(^|\\s)"+t+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}const Ts=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",$s=".sticky-top",Ss=".navbar-toggler";class Ds extends vs{adjustAndStore(e,t,n){const r=t.style[e];t.dataset[e]=r,P(t,{[e]:`${parseFloat(P(t,e))+n}px`})}restore(e,t){const n=t.dataset[e];void 0!==n&&(delete t.dataset[e],P(t,{[e]:n}))}setContainerStyle(e){super.setContainerStyle(e);const t=this.getElement();var n,r;if(r="modal-open",(n=t).classList?n.classList.add(r):ks(n,r)||("string"==typeof n.className?n.className=n.className+" "+r:n.setAttribute("class",(n.className&&n.className.baseVal||"")+" "+r)),!e.scrollBarWidth)return;const o=this.isRTL?"paddingLeft":"paddingRight",a=this.isRTL?"marginLeft":"marginRight";fn(t,Ts).forEach((t=>this.adjustAndStore(o,t,e.scrollBarWidth))),fn(t,$s).forEach((t=>this.adjustAndStore(a,t,-e.scrollBarWidth))),fn(t,Ss).forEach((t=>this.adjustAndStore(a,t,e.scrollBarWidth)))}removeContainerStyle(e){super.removeContainerStyle(e);const t=this.getElement();var n,r;r="modal-open",(n=t).classList?n.classList.remove(r):"string"==typeof n.className?n.className=Ps(n.className,r):n.setAttribute("class",Ps(n.className&&n.className.baseVal||"",r));const o=this.isRTL?"paddingLeft":"paddingRight",a=this.isRTL?"marginLeft":"marginRight";fn(t,Ts).forEach((e=>this.restore(o,e))),fn(t,$s).forEach((e=>this.restore(a,e))),fn(t,Ss).forEach((e=>this.restore(a,e)))}}let Is;function Ms(e){return Is||(Is=new Ds(e)),Is}const Ls=Ds,As=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=g(n,"modal-body"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));As.displayName="ModalBody";const Bs=As,Fs=n.createContext({onHide(){}}),Hs=n.forwardRef((({bsPrefix:e,className:n,contentClassName:r,centered:o,size:a,fullscreen:s,children:i,scrollable:l,...c},u)=>{const d=`${e=g(e,"modal")}-dialog`,f="string"==typeof s?`${e}-fullscreen-${s}`:`${e}-fullscreen`;return(0,p.jsx)("div",{...c,ref:u,className:t()(d,n,a&&`${e}-${a}`,o&&`${d}-centered`,l&&`${d}-scrollable`,s&&f),children:(0,p.jsx)("div",{className:t()(`${e}-content`,r),children:i})})}));Hs.displayName="ModalDialog";const _s=Hs,Ks=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=g(n,"modal-footer"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));Ks.displayName="ModalFooter";const Ws=Ks,Vs=n.forwardRef((({closeLabel:e="Close",closeVariant:t,closeButton:r=!1,onHide:o,children:a,...s},i)=>{const l=(0,n.useContext)(Fs),c=$e((()=>{null==l||l.onHide(),null==o||o()}));return(0,p.jsxs)("div",{ref:i,...s,children:[a,r&&(0,p.jsx)(at,{"aria-label":e,variant:t,onClick:c})]})}));Vs.displayName="AbstractModalHeader";const zs=Vs,Us=n.forwardRef((({bsPrefix:e,className:n,closeLabel:r="Close",closeButton:o=!1,...a},s)=>(e=g(e,"modal-header"),(0,p.jsx)(zs,{ref:s,...a,className:t()(n,e),closeLabel:r,closeButton:o}))));Us.displayName="ModalHeader";const qs=Us,Gs=Se("h4"),Xs=n.forwardRef((({className:e,bsPrefix:n,as:r=Gs,...o},a)=>(n=g(n,"modal-title"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));Xs.displayName="ModalTitle";const Ys=Xs;function Zs(e){return(0,p.jsx)(et,{...e,timeout:null})}function Js(e){return(0,p.jsx)(et,{...e,timeout:null})}const Qs=n.forwardRef((({bsPrefix:e,className:r,style:o,dialogClassName:a,contentClassName:s,children:i,dialogAs:l=_s,"data-bs-theme":c,"aria-labelledby":u,"aria-describedby":d,"aria-label":f,show:m=!1,animation:v=!0,backdrop:h=!0,keyboard:x=!0,onEscapeKeyDown:b,onShow:y,onHide:w,container:N,autoFocus:j=!0,enforceFocus:O=!0,restoreFocus:R=!0,restoreFocusOptions:k,onEntered:P,onExit:T,onExiting:$,onEnter:S,onEntering:D,onExited:I,backdropClassName:M,manager:L,...A},B)=>{const[F,H]=(0,n.useState)({}),[_,K]=(0,n.useState)(!1),W=(0,n.useRef)(!1),z=(0,n.useRef)(!1),U=(0,n.useRef)(null),[q,Y]=(0,n.useState)(null),J=re(B,Y),Q=$e(w),ee=E();e=g(e,"modal");const te=(0,n.useMemo)((()=>({onHide:Q})),[Q]);function ne(){return L||Ms({isRTL:ee})}function oe(e){if(!V)return;const t=ne().getScrollbarWidth()>0,n=e.scrollHeight>C(e).documentElement.clientHeight;H({paddingRight:t&&!n?fs():void 0,paddingLeft:!t&&n?fs():void 0})}const ae=$e((()=>{q&&oe(q.dialog)}));qt((()=>{X(window,"resize",ae),null==U.current||U.current()}));const se=()=>{W.current=!0},ie=e=>{W.current&&q&&e.target===q.dialog&&(z.current=!0),W.current=!1},le=()=>{K(!0),U.current=Z(q.dialog,(()=>{K(!1)}))},ce=e=>{"static"!==h?z.current||e.target!==e.currentTarget?z.current=!1:null==w||w():(e=>{e.target===e.currentTarget&&le()})(e)},ue=(0,n.useCallback)((n=>(0,p.jsx)("div",{...n,className:t()(`${e}-backdrop`,M,!v&&"show")})),[v,M,e]),de={...o,...F};return de.display="block",(0,p.jsx)(Fs.Provider,{value:te,children:(0,p.jsx)(Rs,{show:m,ref:J,backdrop:h,container:N,keyboard:!0,autoFocus:j,enforceFocus:O,restoreFocus:R,restoreFocusOptions:k,onEscapeKeyDown:e=>{x?null==b||b(e):(e.preventDefault(),"static"===h&&le())},onShow:y,onHide:w,onEnter:(e,t)=>{e&&oe(e),null==S||S(e,t)},onEntering:(e,t)=>{null==D||D(e,t),G(window,"resize",ae)},onEntered:P,onExit:e=>{null==U.current||U.current(),null==T||T(e)},onExiting:$,onExited:e=>{e&&(e.style.display=""),null==I||I(e),X(window,"resize",ae)},manager:ne(),transition:v?Zs:void 0,backdropTransition:v?Js:void 0,renderBackdrop:ue,renderDialog:n=>(0,p.jsx)("div",{role:"dialog",...n,style:de,className:t()(r,e,_&&`${e}-static`,!v&&"show"),onClick:h?ce:void 0,onMouseUp:ie,"data-bs-theme":c,"aria-label":f,"aria-labelledby":u,"aria-describedby":d,children:(0,p.jsx)(l,{...A,onMouseDown:se,className:a,contentClassName:s,children:i})})})})}));Qs.displayName="Modal";const ei=Object.assign(Qs,{Body:Bs,Header:qs,Title:Ys,Footer:Ws,Dialog:_s,TRANSITION_DURATION:300,BACKDROP_TRANSITION_DURATION:150}),ti=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=g(n,"nav-item"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));ti.displayName="NavItem";const ni=ti,ri=n.forwardRef((({bsPrefix:e,className:n,as:r=Xe,active:o,eventKey:a,disabled:s=!1,...i},l)=>{e=g(e,"nav-link");const[c,u]=Qa({key:vo(a,i.href),active:o,disabled:s,...i});return(0,p.jsx)(r,{...i,...c,ref:l,disabled:s,className:t()(n,e,s&&"disabled",u.isActive&&"active")})}));ri.displayName="NavLink";const oi=ri,ai=n.forwardRef(((e,r)=>{const{as:o="div",bsPrefix:a,variant:s,fill:i=!1,justify:l=!1,navbar:c,navbarScroll:u,className:d,activeKey:m,...v}=f(e,{activeKey:"onSelect"}),h=g(a,"nav");let x,b,y=!1;const w=(0,n.useContext)(zo),E=(0,n.useContext)(Ot);return w?(x=w.bsPrefix,y=null==c||c):E&&({cardHeaderBsPrefix:b}=E),(0,p.jsx)(ss,{as:o,ref:r,activeKey:m,className:t()(d,{[h]:!y,[`${x}-nav`]:y,[`${x}-nav-scroll`]:y&&u,[`${b}-${s}`]:!!b,[`${h}-${s}`]:!!s,[`${h}-fill`]:i,[`${h}-justified`]:l}),...v})}));ai.displayName="Nav";const si=Object.assign(ai,{Item:ni,Link:oi}),ii=n.forwardRef((({bsPrefix:e,className:n,as:r,...o},a)=>{e=g(e,"navbar-brand");const s=r||(o.href?"a":"span");return(0,p.jsx)(s,{...o,ref:a,className:t()(n,e)})}));ii.displayName="NavbarBrand";const li=ii,ci=n.forwardRef((({children:e,bsPrefix:t,...r},o)=>{t=g(t,"navbar-collapse");const a=(0,n.useContext)(zo);return(0,p.jsx)(de,{in:!(!a||!a.expanded),...r,children:(0,p.jsx)("div",{ref:o,className:t,children:e})})}));ci.displayName="NavbarCollapse";const ui=ci,di=n.forwardRef((({bsPrefix:e,className:r,children:o,label:a="Toggle navigation",as:s="button",onClick:i,...l},c)=>{e=g(e,"navbar-toggler");const{onToggle:u,expanded:d}=(0,n.useContext)(zo)||{},f=$e((e=>{i&&i(e),u&&u()}));return"button"===s&&(l.type="button"),(0,p.jsx)(s,{...l,ref:c,onClick:f,"aria-label":a,className:t()(r,e,!d&&"collapsed"),children:o||(0,p.jsx)("span",{className:`${e}-icon`})})}));di.displayName="NavbarToggle";const fi=di,pi=new WeakMap,mi=(e,t)=>{if(!e||!t)return;const n=pi.get(t)||new Map;pi.set(t,n);let r=n.get(e);return r||(r=t.matchMedia(e),r.refCount=0,n.set(r.media,r)),r};function vi(e,t=("undefined"==typeof window?void 0:window)){const r=mi(e,t),[o,a]=(0,n.useState)((()=>!!r&&r.matches));return _o((()=>{let n=mi(e,t);if(!n)return a(!1);let r=pi.get(t);const o=()=>{a(n.matches)};return n.refCount++,n.addListener(o),o(),()=>{n.removeListener(o),n.refCount--,n.refCount<=0&&(null==r||r.delete(n.media)),n=void 0}}),[e]),o}const hi=function(e){const t=Object.keys(e);function r(e,t){return e===t?t:e?`${e} and ${t}`:t}return function(o,a,s){let i;return"object"==typeof o?(i=o,s=a,a=!0):(a=a||!0,i={[o]:a}),vi((0,n.useMemo)((()=>Object.entries(i).reduce(((n,[o,a])=>("up"!==a&&!0!==a||(n=r(n,function(t){let n=e[t];return"number"==typeof n&&(n=`${n}px`),`(min-width: ${n})`}(o))),"down"!==a&&!0!==a||(n=r(n,function(n){const r=function(e){return t[Math.min(t.indexOf(e)+1,t.length-1)]}(n);let o=e[r];return o="number"==typeof o?o-.2+"px":`calc(${o} - 0.2px)`,`(max-width: ${o})`}(o))),n)),"")),[JSON.stringify(i)]),s)}}({xs:0,sm:576,md:768,lg:992,xl:1200,xxl:1400}),xi=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=g(n,"offcanvas-body"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));xi.displayName="OffcanvasBody";const bi=xi,gi={[L]:"show",[A]:"show"},yi=n.forwardRef((({bsPrefix:e,className:r,children:o,in:a=!1,mountOnEnter:s=!1,unmountOnExit:i=!1,appear:l=!1,...c},u)=>(e=g(e,"offcanvas"),(0,p.jsx)(se,{ref:u,addEndListener:Q,in:a,mountOnEnter:s,unmountOnExit:i,appear:l,...c,childRef:W(o),children:(a,s)=>n.cloneElement(o,{...s,className:t()(r,o.props.className,(a===L||a===B)&&`${e}-toggling`,gi[a])})}))));yi.displayName="OffcanvasToggling";const wi=yi,Ei=n.forwardRef((({bsPrefix:e,className:n,closeLabel:r="Close",closeButton:o=!1,...a},s)=>(e=g(e,"offcanvas-header"),(0,p.jsx)(zs,{ref:s,...a,className:t()(n,e),closeLabel:r,closeButton:o}))));Ei.displayName="OffcanvasHeader";const Ni=Ei,Ci=Se("h5"),ji=n.forwardRef((({className:e,bsPrefix:n,as:r=Ci,...o},a)=>(n=g(n,"offcanvas-title"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));ji.displayName="OffcanvasTitle";const Oi=ji;function Ri(e){return(0,p.jsx)(wi,{...e})}function ki(e){return(0,p.jsx)(et,{...e})}const Pi=n.forwardRef((({bsPrefix:e,className:r,children:o,"aria-labelledby":a,placement:s="start",responsive:i,show:l=!1,backdrop:c=!0,keyboard:u=!0,scroll:d=!1,onEscapeKeyDown:f,onShow:m,onHide:v,container:h,autoFocus:x=!0,enforceFocus:b=!0,restoreFocus:y=!0,restoreFocusOptions:w,onEntered:E,onExit:N,onExiting:C,onEnter:j,onEntering:O,onExited:R,backdropClassName:k,manager:P,renderStaticNode:T=!1,...$},S)=>{const D=(0,n.useRef)();e=g(e,"offcanvas");const[I,M]=(0,n.useState)(!1),L=$e(v),A=hi(i||"xs","up");(0,n.useEffect)((()=>{M(i?l&&!A:l)}),[l,i,A]);const B=(0,n.useMemo)((()=>({onHide:L})),[L]),F=(0,n.useCallback)((n=>(0,p.jsx)("div",{...n,className:t()(`${e}-backdrop`,k)})),[k,e]),H=n=>(0,p.jsx)("div",{...n,...$,className:t()(r,i?`${e}-${i}`:e,`${e}-${s}`),"aria-labelledby":a,children:o});return(0,p.jsxs)(p.Fragment,{children:[!I&&(i||T)&&H({}),(0,p.jsx)(Fs.Provider,{value:B,children:(0,p.jsx)(Rs,{show:I,ref:S,backdrop:c,container:h,keyboard:u,autoFocus:x,enforceFocus:b&&!d,restoreFocus:y,restoreFocusOptions:w,onEscapeKeyDown:f,onShow:m,onHide:L,onEnter:(e,...t)=>{e&&(e.style.visibility="visible"),null==j||j(e,...t)},onEntering:O,onEntered:E,onExit:N,onExiting:C,onExited:(e,...t)=>{e&&(e.style.visibility=""),null==R||R(...t)},manager:P||(d?(D.current||(D.current=new Ls({handleContainerOverflow:!1})),D.current):Ms()),transition:Ri,backdropTransition:ki,renderBackdrop:F,renderDialog:H})})]})}));Pi.displayName="Offcanvas";const Ti=Object.assign(Pi,{Body:bi,Header:Ni,Title:Oi}),$i=n.forwardRef((({onHide:e,...t},r)=>{const o=(0,n.useContext)(zo),a=$e((()=>{null==o||null==o.onToggle||o.onToggle(),null==e||e()}));return(0,p.jsx)(Ti,{ref:r,show:!(null==o||!o.expanded),...t,renderStaticNode:!0,onHide:a})}));$i.displayName="NavbarOffcanvas";const Si=$i,Di=n.forwardRef((({className:e,bsPrefix:n,as:r="span",...o},a)=>(n=g(n,"navbar-text"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));Di.displayName="NavbarText";const Ii=Di,Mi=n.forwardRef(((e,r)=>{const{bsPrefix:o,expand:a=!0,variant:s="light",bg:i,fixed:l,sticky:c,className:u,as:d="nav",expanded:m,onToggle:v,onSelect:h,collapseOnSelect:x=!1,...b}=f(e,{expanded:"onToggle"}),y=g(o,"navbar"),w=(0,n.useCallback)(((...e)=>{null==h||h(...e),x&&m&&(null==v||v(!1))}),[h,x,m,v]);void 0===b.role&&"nav"!==d&&(b.role="navigation");let E=`${y}-expand`;"string"==typeof a&&(E=`${E}-${a}`);const N=(0,n.useMemo)((()=>({onToggle:()=>null==v?void 0:v(!m),bsPrefix:y,expanded:!!m,expand:a})),[y,m,a,v]);return(0,p.jsx)(zo.Provider,{value:N,children:(0,p.jsx)(ho.Provider,{value:w,children:(0,p.jsx)(d,{ref:r,...b,className:t()(u,y,a&&E,s&&`${y}-${s}`,i&&`bg-${i}`,c&&`sticky-${c}`,l&&`fixed-${l}`)})})})}));Mi.displayName="Navbar";const Li=Object.assign(Mi,{Brand:li,Collapse:ui,Offcanvas:Si,Text:Ii,Toggle:fi}),Ai=n.forwardRef((({id:e,title:n,children:r,bsPrefix:o,className:a,rootCloseEvent:s,menuRole:i,disabled:l,active:c,renderMenuOnMount:u,menuVariant:d,...f},m)=>{const v=g(void 0,"nav-item");return(0,p.jsxs)(Qo,{ref:m,...f,className:t()(a,v),children:[(0,p.jsx)(Qo.Toggle,{id:e,eventKey:null,active:c,disabled:l,childBsPrefix:o,as:oi,children:n}),(0,p.jsx)(Qo.Menu,{role:i,renderOnMount:u,rootCloseEvent:s,variant:d,children:r})]})}));Ai.displayName="NavDropdown";const Bi=Object.assign(Ai,{Item:Qo.Item,ItemText:Qo.ItemText,Divider:Qo.Divider,Header:Qo.Header}),Fi=()=>{},Hi=n.forwardRef(((e,t)=>{const{flip:r,offset:o,placement:a,containerPadding:s,popperConfig:i={},transition:l,runTransition:c}=e,[u,d]=Le(),[f,p]=Le(),m=Ya(d,t),v=xs(e.container),h=xs(e.target),[x,b]=(0,n.useState)(!e.show),g=Lr(h,u,zr({placement:a,enableEvents:!!e.show,containerPadding:s||5,flip:r,offset:o,arrowElement:f,popperConfig:i}));e.show&&x&&b(!1);const y=e.show||!x;if(function(e,t,{disabled:r,clickTrigger:o}={}){const a=t||Fi;Wr(e,a,{disabled:r,clickTrigger:o});const s=Be((e=>{K(e)&&a(e)}));(0,n.useEffect)((()=>{if(r||null==e)return;const t=C(_r(e));let n=(t.defaultView||window).event;const o=Y(t,"keyup",(e=>{e!==n?s(e):n=void 0}));return()=>{o()}}),[e,r,s])}(u,e.onHide,{disabled:!e.rootClose||e.rootCloseDisabled,clickTrigger:e.rootCloseEvent}),!y)return null;const{onExit:w,onExiting:E,onEnter:N,onEntering:j,onEntered:O}=e;let R=e.children(Object.assign({},g.attributes.popper,{style:g.styles.popper,ref:m}),{popper:g,placement:a,show:!!e.show,arrowProps:Object.assign({},g.attributes.arrow,{style:g.styles.arrow,ref:p})});return R=Ns(l,c,{in:!!e.show,appear:!0,mountOnEnter:!0,unmountOnExit:!0,children:R,onExit:w,onExiting:E,onExited:(...t)=>{b(!0),e.onExited&&e.onExited(...t)},onEnter:N,onEntering:j,onEntered:O}),v?S().createPortal(R,v):null}));Hi.displayName="Overlay";const _i=Hi,Ki=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=g(n,"popover-header"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));Ki.displayName="PopoverHeader";const Wi=Ki,Vi=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=g(n,"popover-body"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));Vi.displayName="PopoverBody";const zi=Vi;function Ui(e,t){let n=e;return"left"===e?n=t?"end":"start":"right"===e&&(n=t?"start":"end"),n}function qi(e="absolute"){return{position:e,top:"0",left:"0",opacity:"0",pointerEvents:"none"}}n.Component;const Gi=n.forwardRef((({bsPrefix:e,placement:n="right",className:r,style:o,children:a,body:s,arrowProps:i,hasDoneInitialMeasure:l,popper:c,show:u,...d},f)=>{const m=g(e,"popover"),v=E(),[h]=(null==n?void 0:n.split("-"))||[],x=Ui(h,v);let b=o;return u&&!l&&(b={...o,...qi(null==c?void 0:c.strategy)}),(0,p.jsxs)("div",{ref:f,role:"tooltip",style:b,"x-placement":h,className:t()(r,m,h&&`bs-popover-${x}`),...d,children:[(0,p.jsx)("div",{className:"popover-arrow",...i}),s?(0,p.jsx)(zi,{children:a}):a]})}));Gi.displayName="Popover";const Xi=Object.assign(Gi,{Header:Wi,Body:zi,POPPER_OFFSET:[0,8]}),Yi=n.forwardRef((({bsPrefix:e,placement:n="right",className:r,style:o,children:a,arrowProps:s,hasDoneInitialMeasure:i,popper:l,show:c,...u},d)=>{e=g(e,"tooltip");const f=E(),[m]=(null==n?void 0:n.split("-"))||[],v=Ui(m,f);let h=o;return c&&!i&&(h={...o,...qi(null==l?void 0:l.strategy)}),(0,p.jsxs)("div",{ref:d,style:h,role:"tooltip","x-placement":m,className:t()(r,e,`bs-tooltip-${v}`),...u,children:[(0,p.jsx)("div",{className:"tooltip-arrow",...s}),(0,p.jsx)("div",{className:`${e}-inner`,children:a})]})}));Yi.displayName="Tooltip";const Zi=Object.assign(Yi,{TOOLTIP_OFFSET:[0,6]}),Ji=n.forwardRef((({children:e,transition:r=et,popperConfig:o={},rootClose:a=!1,placement:s="top",show:i=!1,...l},c)=>{const u=(0,n.useRef)({}),[d,f]=(0,n.useState)(null),[m,v]=function(e){const t=(0,n.useRef)(null),r=g(void 0,"popover"),o=g(void 0,"tooltip"),a=(0,n.useMemo)((()=>({name:"offset",options:{offset:()=>{if(e)return e;if(t.current){if(ks(t.current,r))return Xi.POPPER_OFFSET;if(ks(t.current,o))return Zi.TOOLTIP_OFFSET}return[0,0]}}})),[e,r,o]);return[t,[a]]}(l.offset),h=re(c,m),x=!0===r?et:r||void 0,b=$e((e=>{f(e),null==o||null==o.onFirstUpdate||o.onFirstUpdate(e)}));return _o((()=>{d&&l.target&&(null==u.current.scheduleUpdate||u.current.scheduleUpdate())}),[d,l.target]),(0,n.useEffect)((()=>{i||f(null)}),[i]),(0,p.jsx)(_i,{...l,ref:h,popperConfig:{...o,modifiers:v.concat(o.modifiers||[]),onFirstUpdate:b},transition:x,rootClose:a,placement:s,show:i,children:(a,{arrowProps:s,popper:i,show:l})=>{var c;!function(e,t){const{ref:n}=e,{ref:r}=t;e.ref=n.__wrapped||(n.__wrapped=e=>n(oe(e))),t.ref=r.__wrapped||(r.__wrapped=e=>r(oe(e)))}(a,s);const f=null==i?void 0:i.placement,p=Object.assign(u.current,{state:null==i?void 0:i.state,scheduleUpdate:null==i?void 0:i.update,placement:f,outOfBoundaries:(null==i||null==(c=i.state)||null==(c=c.modifiersData.hide)?void 0:c.isReferenceHidden)||!1,strategy:o.strategy}),m=!!d;return"function"==typeof e?e({...a,placement:f,show:l,...!r&&l&&{className:"show"},popper:p,arrowProps:s,hasDoneInitialMeasure:m}):n.cloneElement(e,{...a,placement:f,arrowProps:s,popper:p,hasDoneInitialMeasure:m,className:t()(e.props.className,!r&&l&&"show"),style:{...e.props.style,...a.style}})}})}));Ji.displayName="Overlay";const Qi=Ji;function el(e,t,n){const[r]=t,o=r.currentTarget,a=r.relatedTarget||r.nativeEvent[n];a&&a===o||Ar(o,a)||e(...t)}nt().oneOf(["click","hover","focus"]);const tl=({trigger:e=["hover","focus"],overlay:t,children:r,popperConfig:o={},show:a,defaultShow:s=!1,onToggle:i,delay:l,placement:c,flip:u=c&&-1!==c.indexOf("auto"),...f})=>{const m=(0,n.useRef)(null),v=re(m,W(r)),h=Yt(),x=(0,n.useRef)(""),[b,g]=d(a,s,i),y=function(e){return e&&"object"==typeof e?e:{show:e,hide:e}}(l),{onFocus:w,onBlur:E,onClick:N}="function"!=typeof r?n.Children.only(r).props:{},C=(0,n.useCallback)((()=>{h.clear(),x.current="show",y.show?h.set((()=>{"show"===x.current&&g(!0)}),y.show):g(!0)}),[y.show,g,h]),j=(0,n.useCallback)((()=>{h.clear(),x.current="hide",y.hide?h.set((()=>{"hide"===x.current&&g(!1)}),y.hide):g(!1)}),[y.hide,g,h]),O=(0,n.useCallback)(((...e)=>{C(),null==w||w(...e)}),[C,w]),R=(0,n.useCallback)(((...e)=>{j(),null==E||E(...e)}),[j,E]),k=(0,n.useCallback)(((...e)=>{g(!b),null==N||N(...e)}),[N,g,b]),P=(0,n.useCallback)(((...e)=>{el(C,e,"fromElement")}),[C]),T=(0,n.useCallback)(((...e)=>{el(j,e,"toElement")}),[j]),$=null==e?[]:[].concat(e),S={ref:e=>{v(oe(e))}};return-1!==$.indexOf("click")&&(S.onClick=k),-1!==$.indexOf("focus")&&(S.onFocus=O,S.onBlur=R),-1!==$.indexOf("hover")&&(S.onMouseOver=P,S.onMouseOut=T),(0,p.jsxs)(p.Fragment,{children:["function"==typeof r?r(S):(0,n.cloneElement)(r,S),(0,p.jsx)(Qi,{...f,show:b,onHide:j,flip:u,placement:c,popperConfig:o,target:m.current,children:t})]})},nl=n.forwardRef((({active:e=!1,disabled:n=!1,className:r,style:o,activeLabel:a="(current)",children:s,linkStyle:i,linkClassName:l,as:c=Xe,...u},d)=>{const f=e||n?"span":c;return(0,p.jsx)("li",{ref:d,style:o,className:t()(r,"page-item",{active:e,disabled:n}),children:(0,p.jsxs)(f,{className:t()("page-link",l),style:i,...u,children:[s,e&&a&&(0,p.jsx)("span",{className:"visually-hidden",children:a})]})})}));nl.displayName="PageItem";const rl=nl;function ol(e,t,r=e){const o=n.forwardRef((({children:e,...n},o)=>(0,p.jsxs)(nl,{...n,ref:o,children:[(0,p.jsx)("span",{"aria-hidden":"true",children:e||t}),(0,p.jsx)("span",{className:"visually-hidden",children:r})]})));return o.displayName=e,o}const al=ol("First","«"),sl=ol("Prev","‹","Previous"),il=ol("Ellipsis","…","More"),ll=ol("Next","›"),cl=ol("Last","»"),ul=n.forwardRef((({bsPrefix:e,className:n,size:r,...o},a)=>{const s=g(e,"pagination");return(0,p.jsx)("ul",{ref:a,...o,className:t()(n,s,r&&`${s}-${r}`)})}));ul.displayName="Pagination";const dl=Object.assign(ul,{First:al,Prev:sl,Ellipsis:il,Item:rl,Next:ll,Last:cl});function fl({animation:e,bg:n,bsPrefix:r,size:o,...a}){r=g(r,"placeholder");const[{className:s,...i}]=an(a);return{...i,className:t()(s,e?`${r}-${e}`:r,o&&`${r}-${o}`,n&&`bg-${n}`)}}const pl=n.forwardRef(((e,t)=>{const n=fl(e);return(0,p.jsx)(ht,{...n,ref:t,disabled:!0,tabIndex:-1})}));pl.displayName="PlaceholderButton";const ml=pl,vl=n.forwardRef((({as:e="span",...t},n)=>{const r=fl(t);return(0,p.jsx)(e,{...r,ref:n})}));vl.displayName="Placeholder";const hl=Object.assign(vl,{Button:ml}),xl=1e3;function bl(e,t,n){const r=(e-t)/(n-t)*100;return Math.round(r*xl)/xl}function gl({min:e,now:n,max:r,label:o,visuallyHidden:a,striped:s,animated:i,className:l,style:c,variant:u,bsPrefix:d,...f},m){return(0,p.jsx)("div",{ref:m,...f,role:"progressbar",className:t()(l,`${d}-bar`,{[`bg-${u}`]:u,[`${d}-bar-animated`]:i,[`${d}-bar-striped`]:i||s}),style:{width:`${bl(n,e,r)}%`,...c},"aria-valuenow":n,"aria-valuemin":e,"aria-valuemax":r,children:a?(0,p.jsx)("span",{className:"visually-hidden",children:o}):o})}const yl=n.forwardRef((({isChild:e=!1,...r},o)=>{const a={min:0,max:100,animated:!1,visuallyHidden:!1,striped:!1,...r};if(a.bsPrefix=g(a.bsPrefix,"progress"),e)return gl(a,o);const{min:s,now:i,max:l,label:c,visuallyHidden:u,striped:d,animated:f,bsPrefix:m,variant:v,className:h,children:x,...b}=a;return(0,p.jsx)("div",{ref:o,...b,className:t()(h,m),children:x?tn(x,(e=>(0,n.cloneElement)(e,{isChild:!0}))):gl({min:s,now:i,max:l,label:c,visuallyHidden:u,striped:d,animated:f,bsPrefix:m,variant:v},o)})}));yl.displayName="ProgressBar";const wl=yl,El=n.forwardRef((({bsPrefix:e,className:r,children:o,aspectRatio:a="1x1",style:s,...i},l)=>{e=g(e,"ratio");const c="number"==typeof a;return(0,p.jsx)("div",{ref:l,...i,style:{...s,...c&&{"--bs-aspect-ratio":(u=a,u<=0?"100%":u<1?100*u+"%":`${u}%`)}},className:t()(e,r,!c&&`${e}-${a}`),children:n.Children.only(o)});var u}));El.displayName="Ratio";const Nl=El,Cl=n.forwardRef((({bsPrefix:e,className:n,as:r="div",...o},a)=>{const s=g(e,"row"),i=y(),l=w(),c=`${s}-cols`,u=[];return i.forEach((e=>{const t=o[e];let n;delete o[e],null!=t&&"object"==typeof t?({cols:n}=t):n=t;const r=e!==l?`-${e}`:"";null!=n&&u.push(`${c}${r}-${n}`)})),(0,p.jsx)(r,{ref:a,...o,className:t()(n,s,...u)})}));Cl.displayName="Row";const jl=Cl,Ol=n.forwardRef((({bsPrefix:e,variant:n,animation:r="border",size:o,as:a="div",className:s,...i},l)=>{const c=`${e=g(e,"spinner")}-${r}`;return(0,p.jsx)(a,{ref:l,...i,className:t()(s,c,o&&`${c}-${o}`,n&&`text-${n}`)})}));Ol.displayName="Spinner";const Rl=Ol,kl={id:nt().string,toggleLabel:nt().string,href:nt().string,target:nt().string,onClick:nt().func,title:nt().node.isRequired,type:nt().string,disabled:nt().bool,align:ta,menuRole:nt().string,renderMenuOnMount:nt().bool,rootCloseEvent:nt().string,flip:nt().bool,bsPrefix:nt().string,variant:nt().string,size:nt().string},Pl=n.forwardRef((({id:e,bsPrefix:t,size:n,variant:r,title:o,type:a="button",toggleLabel:s="Toggle dropdown",children:i,onClick:l,href:c,target:u,menuRole:d,renderMenuOnMount:f,rootCloseEvent:m,flip:v,...h},x)=>(0,p.jsxs)(Qo,{ref:x,...h,as:bt,children:[(0,p.jsx)(ht,{size:n,variant:r,disabled:h.disabled,bsPrefix:t,href:c,target:u,onClick:l,type:a,children:o}),(0,p.jsx)(Qo.Toggle,{split:!0,id:e,size:n,variant:r,disabled:h.disabled,childBsPrefix:t,children:(0,p.jsx)("span",{className:"visually-hidden",children:s})}),(0,p.jsx)(Qo.Menu,{role:d,renderOnMount:f,rootCloseEvent:m,flip:v,children:i})]})));Pl.propTypes=kl,Pl.displayName="SplitButton";const Tl=Pl,$l=function(e){return"function"==typeof n.useId?(to||(console.warn("In React 18, SSRProvider is not necessary and is a noop. You can remove it from your app."),to=!0),n.createElement(n.Fragment,null,e.children)):n.createElement(eo,e)};function Sl(e,t=m,n=v){const r=[];return Object.entries(e).forEach((([e,o])=>{null!=o&&("object"==typeof o?t.forEach((t=>{const a=o[t];if(null!=a){const o=t!==n?`-${t}`:"";r.push(`${e}${o}-${a}`)}})):r.push(`${e}-${o}`))})),r}const Dl=n.forwardRef((({as:e="div",bsPrefix:n,className:r,direction:o,gap:a,...s},i)=>{n=g(n,"horizontal"===o?"hstack":"vstack");const l=y(),c=w();return(0,p.jsx)(e,{...s,ref:i,className:t()(r,n,...Sl({gap:a},l,c))})}));Dl.displayName="Stack";const Il=Dl,Ml=["active","eventKey","mountOnEnter","transition","unmountOnExit","role","onEnter","onEntering","onEntered","onExit","onExiting","onExited"],Ll=["activeKey","getControlledId","getControllerId"],Al=["as"];function Bl(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}function Fl(e){let{active:t,eventKey:r,mountOnEnter:o,transition:a,unmountOnExit:s,role:i="tabpanel",onEnter:l,onEntering:c,onEntered:u,onExit:d,onExiting:f,onExited:p}=e,m=Bl(e,Ml);const v=(0,n.useContext)(Za);if(!v)return[Object.assign({},m,{role:i}),{eventKey:r,isActive:t,mountOnEnter:o,transition:a,unmountOnExit:s,onEnter:l,onEntering:c,onEntered:u,onExit:d,onExiting:f,onExited:p}];const{activeKey:h,getControlledId:x,getControllerId:b}=v,g=Bl(v,Ll),y=vo(r);return[Object.assign({},m,{role:i,id:x(r),"aria-labelledby":b(r)}),{eventKey:r,isActive:null==t&&null!=y?vo(h)===y:t,transition:a||g.transition,mountOnEnter:null!=o?o:g.mountOnEnter,unmountOnExit:null!=s?s:g.unmountOnExit,onEnter:l,onEntering:c,onEntered:u,onExit:d,onExiting:f,onExited:p}]}const Hl=n.forwardRef(((e,t)=>{let{as:n="div"}=e,r=Bl(e,Al);const[o,{isActive:a,onEnter:s,onEntering:i,onEntered:l,onExit:c,onExiting:u,onExited:d,mountOnEnter:f,unmountOnExit:m,transition:v=bs}]=Fl(r);return(0,p.jsx)(Za.Provider,{value:null,children:(0,p.jsx)(ho.Provider,{value:null,children:(0,p.jsx)(v,{in:a,onEnter:s,onEntering:i,onEntered:l,onExit:c,onExiting:u,onExited:d,mountOnEnter:f,unmountOnExit:m,children:(0,p.jsx)(n,Object.assign({},o,{ref:t,hidden:!a,"aria-hidden":!a}))})})})}));Hl.displayName="TabPanel";const _l=e=>{const{id:t,generateChildId:r,onSelect:o,activeKey:a,defaultActiveKey:s,transition:i,mountOnEnter:l,unmountOnExit:c,children:u}=e,[d,f]=pn(a,s,o),m=ao(t),v=(0,n.useMemo)((()=>r||((e,t)=>m?`${m}-${t}-${e}`:null)),[m,r]),h=(0,n.useMemo)((()=>({onSelect:f,activeKey:d,transition:i,mountOnEnter:l||!1,unmountOnExit:c||!1,getControlledId:e=>v(e,"tabpane"),getControllerId:e=>v(e,"tab")})),[f,d,i,l,c,v]);return(0,p.jsx)(Za.Provider,{value:h,children:(0,p.jsx)(ho.Provider,{value:f||null,children:u})})};_l.Panel=Hl;const Kl=_l;function Wl(e){return"boolean"==typeof e?e?et:bs:e}const Vl=({transition:e,...t})=>(0,p.jsx)(Kl,{...t,transition:Wl(e)});Vl.displayName="TabContainer";const zl=Vl,Ul=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=g(n,"tab-content"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));Ul.displayName="TabContent";const ql=Ul,Gl=n.forwardRef((({bsPrefix:e,transition:n,...r},o)=>{const[{className:a,as:s="div",...i},{isActive:l,onEnter:c,onEntering:u,onEntered:d,onExit:f,onExiting:m,onExited:v,mountOnEnter:h,unmountOnExit:x,transition:b=et}]=Fl({...r,transition:Wl(n)}),y=g(e,"tab-pane");return(0,p.jsx)(Za.Provider,{value:null,children:(0,p.jsx)(ho.Provider,{value:null,children:(0,p.jsx)(b,{in:l,onEnter:c,onEntering:u,onEntered:d,onExit:f,onExiting:m,onExited:v,mountOnEnter:h,unmountOnExit:x,children:(0,p.jsx)(s,{...i,ref:o,className:t()(a,y,l&&"active")})})})})}));Gl.displayName="TabPane";const Xl=Gl,Yl={eventKey:nt().oneOfType([nt().string,nt().number]),title:nt().node.isRequired,disabled:nt().bool,tabClassName:nt().string,tabAttrs:nt().object},Zl=()=>{throw new Error("ReactBootstrap: The `Tab` component is not meant to be rendered! It's an abstract component that is only valid as a direct Child of the `Tabs` Component. For custom tabs components use TabPane and TabsContainer directly")};Zl.propTypes=Yl;const Jl=Object.assign(Zl,{Container:zl,Content:ql,Pane:Xl}),Ql=n.forwardRef((({bsPrefix:e,className:n,striped:r,bordered:o,borderless:a,hover:s,size:i,variant:l,responsive:c,...u},d)=>{const f=g(e,"table"),m=t()(n,f,l&&`${f}-${l}`,i&&`${f}-${i}`,r&&`${f}-${"string"==typeof r?`striped-${r}`:"striped"}`,o&&`${f}-bordered`,a&&`${f}-borderless`,s&&`${f}-hover`),v=(0,p.jsx)("table",{...u,className:m,ref:d});if(c){let e=`${f}-responsive`;return"string"==typeof c&&(e=`${e}-${c}`),(0,p.jsx)("div",{className:e,children:v})}return v}));Ql.displayName="Table";const ec=Ql;function tc(e){let t;return nn(e,(e=>{null==t&&(t=e.props.eventKey)})),t}function nc(e){const{title:t,eventKey:n,disabled:r,tabClassName:o,tabAttrs:a,id:s}=e.props;return null==t?null:(0,p.jsx)(ni,{as:"li",role:"presentation",children:(0,p.jsx)(oi,{as:"button",type:"button",eventKey:n,disabled:r,id:s,className:o,...a,children:t})})}const rc=e=>{const{id:t,onSelect:n,transition:r,mountOnEnter:o=!1,unmountOnExit:a=!1,variant:s="tabs",children:i,activeKey:l=tc(i),...c}=f(e,{activeKey:"onSelect"});return(0,p.jsxs)(Kl,{id:t,activeKey:l,onSelect:n,transition:Wl(r),mountOnEnter:o,unmountOnExit:a,children:[(0,p.jsx)(si,{id:t,...c,role:"tablist",as:"ul",variant:s,children:tn(i,nc)}),(0,p.jsx)(ql,{children:tn(i,(e=>{const t={...e.props};return delete t.title,delete t.disabled,delete t.tabClassName,delete t.tabAttrs,(0,p.jsx)(Xl,{...t})}))})]})};rc.displayName="Tabs";const oc=rc,ac={[L]:"showing",[B]:"showing show"},sc=n.forwardRef(((e,t)=>(0,p.jsx)(et,{...e,ref:t,transitionClasses:ac})));sc.displayName="ToastFade";const ic=sc,lc=n.createContext({onClose(){}}),cc=n.forwardRef((({bsPrefix:e,closeLabel:r="Close",closeVariant:o,closeButton:a=!0,className:s,children:i,...l},c)=>{e=g(e,"toast-header");const u=(0,n.useContext)(lc),d=$e((e=>{null==u||null==u.onClose||u.onClose(e)}));return(0,p.jsxs)("div",{ref:c,...l,className:t()(e,s),children:[i,a&&(0,p.jsx)(at,{"aria-label":r,variant:o,onClick:d,"data-dismiss":"toast"})]})}));cc.displayName="ToastHeader";const uc=cc,dc=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=g(n,"toast-body"),(0,p.jsx)(r,{ref:a,className:t()(e,n),...o}))));dc.displayName="ToastBody";const fc=dc,pc=n.forwardRef((({bsPrefix:e,className:r,transition:o=ic,show:a=!0,animation:s=!0,delay:i=5e3,autohide:l=!1,onClose:c,onEntered:u,onExit:d,onExiting:f,onEnter:m,onEntering:v,onExited:h,bg:x,...b},y)=>{e=g(e,"toast");const w=(0,n.useRef)(i),E=(0,n.useRef)(c);(0,n.useEffect)((()=>{w.current=i,E.current=c}),[i,c]);const N=Yt(),C=!(!l||!a),j=(0,n.useCallback)((()=>{C&&(null==E.current||E.current())}),[C]);(0,n.useEffect)((()=>{N.set(j,w.current)}),[N,j]);const O=(0,n.useMemo)((()=>({onClose:c})),[c]),R=!(!o||!s),k=(0,p.jsx)("div",{...b,ref:y,className:t()(e,r,x&&`bg-${x}`,!R&&(a?"show":"hide")),role:"alert","aria-live":"assertive","aria-atomic":"true"});return(0,p.jsx)(lc.Provider,{value:O,children:R&&o?(0,p.jsx)(o,{in:a,onEnter:m,onEntering:v,onEntered:u,onExit:d,onExiting:f,onExited:h,unmountOnExit:!0,children:k}):k})}));pc.displayName="Toast";const mc=Object.assign(pc,{Body:fc,Header:uc}),vc={"top-start":"top-0 start-0","top-center":"top-0 start-50 translate-middle-x","top-end":"top-0 end-0","middle-start":"top-50 start-0 translate-middle-y","middle-center":"top-50 start-50 translate-middle","middle-end":"top-50 end-0 translate-middle-y","bottom-start":"bottom-0 start-0","bottom-center":"bottom-0 start-50 translate-middle-x","bottom-end":"bottom-0 end-0"},hc=n.forwardRef((({bsPrefix:e,position:n,containerPosition:r,className:o,as:a="div",...s},i)=>(e=g(e,"toast-container"),(0,p.jsx)(a,{ref:i,...s,className:t()(e,n&&vc[n],r&&`position-${r}`,o)}))));hc.displayName="ToastContainer";const xc=hc,bc=()=>{},gc=n.forwardRef((({bsPrefix:e,name:n,className:r,checked:o,type:a,onChange:s,value:i,disabled:l,id:c,inputRef:u,...d},f)=>(e=g(e,"btn-check"),(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("input",{className:e,name:n,type:a,value:i,ref:u,autoComplete:"off",checked:!!o,disabled:!!l,onChange:s||bc,id:c}),(0,p.jsx)(ht,{...d,ref:f,className:t()(r,l&&"disabled"),type:void 0,role:void 0,as:"label",htmlFor:c})]}))));gc.displayName="ToggleButton";const yc=gc,wc=n.forwardRef(((e,t)=>{const{children:r,type:o="radio",name:a,value:s,onChange:i,vertical:c=!1,...u}=f(e,{value:"onChange"}),d=()=>null==s?[]:[].concat(s);return"radio"!==o||a||l()(!1),(0,p.jsx)(bt,{...u,ref:t,vertical:c,children:tn(r,(e=>{const t=d(),{value:r,onChange:s}=e.props;return n.cloneElement(e,{type:o,name:e.name||a,checked:-1!==t.indexOf(r),onChange:ee(s,(e=>((e,t)=>{if(!i)return;const n=d(),r=-1!==n.indexOf(e);"radio"!==o?i(r?n.filter((t=>t!==e)):[...n,e],t):r||i(e,t)})(r,e)))})}))})}));wc.displayName="ToggleButtonGroup";const Ec=Object.assign(wc,{Button:yc})})(),a})()));